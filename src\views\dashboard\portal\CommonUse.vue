<template style="background: #f5f6fa">
  <div class="typical-home" ref="portaletDiv">
    <a-spin v-if="spinning" :spinning="spinning" :delay="delayTime" tip="loading..."></a-spin>

    <a-row v-else :gutter="[16, 16]" class="top-list">
      <a-col v-for="(item, index) in dataList" :key="index" :xxl="8" :xl="4" :lg="8">
        <a-card :bordered="false" @click="addTabMenu(item.path)" class="card">
          <div :style="{ background: getMenuColor(index) }" class="icon-box">
            <SvgIcon :iconClass="item.icon" />
          </div>
          <span>{{ item.title }}</span>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
  export default {
    name: 'DashboardIndex',
    props: {
      bannerType: {
        type: String,
        default: '1'
      }
    },
    data() {
      return {
        visible: false,
        spinning: false,
        delayTime: 100,
        dataList: [],
        changyongResultMenus: [], // 添加常用应用查询结果集合
        menus: [],
        sortable: undefined,
        buildMenus: [], // 重构菜单结构，用于首页显示
        activeChangyongTabKey: 'tab0',
        colorList: ['#5584fd', '#3470ff', '#ff8801', '#00d6b9', '#7e3bf3']
      }
    },
    computed: {},
    mounted() {},
    watch: {},
    created() {
      this.getDataList()
    },
    methods: {
      // 递归属性结构转化为扁平结构
      findIndexArray(data, indexArray, all) {
        let arr = Array.from(indexArray)
        for (let i = 0, len = data.length; i < len; i++) {
          arr.push(data[i].route)
          let children = data[i].children

          if (children && children.length && data[i].type !== 3) {
            this.findIndexArray(children, arr, all)
          } else {
            let joinArr = arr.join('/')
            if (!all.includes(joinArr)) {
              // all.push(joinArr);
              all.push({
                path: joinArr,
                icon: data[i].icon,
                title: data[i].menuName
              })
            }
          }
          arr.pop()
        }
        return all
      },

      /* 获取当前用户有权限的路由 */
      getDataList() {
        this.dataList = this.findIndexArray(JSON.parse(localStorage.getItem('asyncRouters')), [], [])
      },
      getMenuColor(index) {
        return this.colorList[index % 5]
      },
      addTabMenu(path) {
        this.$router.push({
          path: path
        })
      }
    }
  }
</script>
<style lang="less" scoped>
  .ant-list-item-drag {
    background: #ffffff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    box-sizing: border-box;
    z-index: 9999;
    border: 1px solid #dee0e3;
  }

  .common-iconbtn .anticon {
    margin: 0px 5px;
    color: rgba(0, 0, 0, 0.35);
    font-size: 16px;
  }

  .common-iconbtn .anticon-menu {
    cursor: move;
  }

  .card {
    // height: 100px;
    cursor: pointer;
    border-radius: 5px;

    &:hover {
      box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
    }
  }

  .typical-home {
    color: #666 !important;
    background-color: #f0f2f5;
    padding: 5px 10px;
    // margin: 5px 0;
    overflow: auto;
    height: calc(100vh - 115px);

    // 顶部列表
    .top-list {
      height: auto;

      /deep/ .ant-card-body {
        height: 66px;
        padding: 13px;
        border-radius: 4px;

        display: flex;
        align-items: center;
        span {
          color: #333;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          flex: 1;
        }
        .icon-box {
          color: #fff;
          width: 40px;
          height: 40px;
          font-size: 24px;
          border-radius: 16px;
          margin-right: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      /deep/ .ant-col:last-child {
        cursor: pointer;
        .add-plus {
          color: #abafb5;
          border: 1px dashed #dadada;
          background: #f3f3f3;
        }
      }
    }
  }
</style>
