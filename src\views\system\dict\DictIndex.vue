<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="关键字">
        <a-input
          v-model="queryParam.keywords"
          placeholder="请输入字典名称/字典编码"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>
      <a-form-item label="状态">
        <a-select placeholder="字典状态" v-model="queryParam.isDisabled" style="width: 100%">
          <a-select-option v-for="(d, index) in statusOptions" :key="index" :value="d.dictValue">
            {{ d.dictLabel }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="字典管理"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @selectChange="selectChange"
          @handlePageChange="handlePageChange"
          :expand-config="{ accordion: true }"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="primary" @click="importData">
              <a-icon type="import" />
              导入
            </a-button>
            <a-button v-if="isChecked" @click="exportData">
              <a-icon type="export" />
              导出
            </a-button>
            <a-button type="" @click="handleRefreshCache">
              <a-icon type="redo" />
              刷新缓存
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>
      </template>
    </VxeTableForm>

    <!-- 修改 -->
    <dict-type-Edit-form
      v-if="showEditModal"
      ref="dictTypeEditForm"
      :statusOptions="statusOptions"
      @ok="getList"
      @close="showEditModal = false"
    />
    <!-- 添加 -->
    <dict-type-add-form
      v-if="showAddModal"
      ref="dictTypeAddForm"
      :statusOptions="statusOptions"
      @ok="getList"
      @close="showAddModal = false"
    />

    <ImportExportJsonModal
      v-if="showImportExportJsonModal"
      ref="importExportJsonModalRef"
      @sendImportRequest="sendImportRequest"
      @afterImport="getList"
      @close="showImportExportJsonModal = false"
    />
  </div>
</template>
<script>
  import { listType, delType, dictStatus, exportType, cleanCache, exportDict, importDict } from '@/api/system/dict/type'
  import DictTypeEditForm from './modules/DictTypeEditForm'
  import DictTypeAddForm from './modules/DictTypeAddForm'
  import DictDataIndex from './DictDataIndex'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import ImportExportJsonModal from '@/views/util/ImportExportJsonModal'

  export default {
    name: 'Dict',
    components: {
      DictTypeEditForm,
      DictTypeAddForm,
      DictDataIndex,
      VxeTable,
      VxeTableForm,
      ImportExportJsonModal,
    },
    data() {
      return {
        showImportExportJsonModal: false,
        showAddModal: false,
        showEditModal: false,
        list: [],
        selectedRowKeys: [],
        selectedRows: [],
        // 高级搜索 展开/关闭
        advanced: false,
        // 非单个禁用
        single: true,
        // 非多个禁用
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,
        // 展开的行，控制属性 这里控制
        expandedRowKeys: [],
        //展开行初始化字典名称
        initDictName: '',
        //展开行初始化字典编码
        initDictCode: '',
        //刷新字典数据
        refreshData: 0,
        // 状态数据字典
        statusOptions: [
          { dictLabel: '正常', dictValue: '0', isDisabled: '0' },
          { dictLabel: '停用', dictValue: '1', isDisabled: '1' },
        ],
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        // 日期范围
        dateRange: [],
        queryParam: {
          pageNum: 1,
          pageSize: 10,
          isDisabled: undefined, //是否停用（0正常 1停用）
          keywords: undefined, //字典名称或字典编码
          sort: [], //排序字段列表
        },
        columns: [
          { type: 'checkbox', width: 30, align: 'center' },
          {
            type: 'expand',
            width: 40,
            slots: {
              content: ({ row, rowIndex }) => {
                return (
                  <dict-data-index
                    style='padding:10px;background-color:#FAFAFA'
                    title={row.dictName}
                    dictCode={row.dictCode}
                    refreshData={this.refreshData}
                  />
                )
              },
            },
          },
          {
            title: '字典名称',
            field: 'dictName',
            showOverflow: 'tooltip',
            align: 'left',
          },
          {
            title: '字典编码',
            field: 'dictCode',
            showOverflow: 'tooltip',
            align: 'center',
          },
          {
            title: '备注',
            field: 'remark',
            showOverflow: 'tooltip',
            align: 'left',
          },
          {
            title: '创建时间',
            field: 'createdTime',
            showOverflow: 'tooltip',
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.parseTime(row.createdTime)
              },
            },
          },
          // onChange={() => this.onChangeStatus($event, row)}
          {
            title: '状态',
            field: 'isDisabled',
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a-switch
                      slot='actions'
                      size='small'
                      style={{ 'background-color': row.isDisabled == 0 ? '#52c41a' : '#fc011a' }}
                      checked={row.isDisabled == 0}
                      onChange={() => this.onChangeStatus(row)}
                    />
                    <span
                      style={{
                        'font-size': '12px',
                        color: row.isDisabled == 0 ? '#52c41a' : '#fc011a',
                      }}
                    >
                      {row.isDisabled == '0' ? '正常' : '停用'}
                    </span>
                  </span>
                )
              },
            },
          },
          {
            title: '操作',
            field: 'operate',
            width: 100,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a-button type='link' onClick={() => this.handleUpdate(row, undefined)}>
                      修改
                    </a-button>
                    <a-divider type='vertical' />
                    <a-button type='link' onClick={() => this.handleDelete(row)}>
                      删除
                    </a-button>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    filters: {},
    created() {
      this.getList()
    },
    computed: {},
    watch: {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = !pageSize ? 9 : pageSize
        this.getList()
      },
      exportData() {
        const dictIds = this.ids
        const dictNames = this.names
        this.showImportExportJsonModal = true
        let that = this
        this.$nextTick(() => {
          that.$refs.importExportJsonModalRef.showExport({
            action: 'export',
            title: `导出字典【${dictNames}】`,
          })
          that.$refs.importExportJsonModalRef.setModalLoading(true)
          exportDict(dictIds)
            .then(res => {
              that.$refs.importExportJsonModalRef.setContent(res.data)
              that.$refs.importExportJsonModalRef.setModalLoading(false)
            })
            .catch(err => {
              that.$refs.importExportJsonModalRef.setModalLoading(false)
            })
        })
      },
      importData() {
        this.showImportExportJsonModal = true
        this.$nextTick(() => {
          this.$refs.importExportJsonModalRef.showImport({
            action: 'import',
            title: `导入字典`,
            content: '',
          })
        })
      },
      sendImportRequest(param) {
        importDict(param.content)
          .then(res => {
            this.$refs.importExportJsonModalRef.handleImportResult(res)
          })
          .catch(err => {
            this.$refs.importExportJsonModalRef.setLoading(false)
            if (err) {
              console.log(err)
            }
          })
      },
      /** 查询定时任务列表 */
      getList() {
        this.expandedRowKeys = []
        this.initDictName = ''
        this.initDictCode = ''
        this.showAddModal = false
        this.showEditModal = false
        this.loading = true

        this.selectChange({ records: [] })

        listType(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      // 参数系统内置字典翻译
      statusFormat(row) {
        return this.selectDictLabel(this.statusOptions, row.isDisabled)
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      handleAdd() {
        this.showAddModal = true
        this.$nextTick(() => this.$refs.dictTypeAddForm.handleAdd())
      },
      handleUpdate(record, ids) {
        this.showEditModal = true
        this.$nextTick(() => this.$refs.dictTypeEditForm.handleUpdate(record, ids))
      },
      /** 状态启用 **/
      onChangeStatus(record) {
        var that = this
        const dictId = record.dictId
        let isDisabled = 0
        let recordIsDisabled = !record.isDisabled
        isDisabled = recordIsDisabled ? 1 : 0
        let isDisabledName = isDisabled == 1 ? '停用' : '启用'
        this.$confirm({
          title: '是否"' + isDisabledName + '"所选中数据?',
          content: '当前选中的数据',
          onOk() {
            return dictStatus(isDisabled, dictId).then(res => {
              if (res.code == 200 && res.success == true) {
                that.getList()
                that.$message.success(record.dictName + '状态"' + isDisabledName + '"成功！')
              } else {
                that.$message.success('"' + isDisabledName + '"失败:', res.message)
              }
            })
          },
          onCancel() {},
        })
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = []
        this.queryParam = {
          pageNum: 1,
          pageSize: 10,
          isDisabled: undefined, //是否停用（0正常 1停用）
          keywords: undefined, //字典名称或字典编码
          sort: [], //排序字段列表
        }
        this.initDictName = ''
        this.initDictCode = ''
        this.refreshData = 2
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.dictId)
        this.names = valObj.records.map(item => item.dictName)
        this.isChecked = !!valObj.records.length
      },

      toggleAdvanced() {
        this.advanced = !this.advanced
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const dictIds = row.dictId || this.ids
        const dictNames = row.dictName || this.names
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中字典编号为"' + dictNames + '"的数据',
          onOk() {
            return delType(dictIds).then(() => {
              that.selectChange({ records: [] })
              that.getList()
              that.$message.success('删除成功', 3)
            })
          },
          onCancel() {},
        })
      },
      /** 导出按钮操作 */
      handleExport() {
        var that = this
        this.$confirm({
          title: '是否确认导出?',
          content: '此操作将导出当前条件下所有数据而非选中数据',
          onOk() {
            return exportType(that.queryParam).then(response => {
              that.download(response.msg)
              that.$message.success('导出成功', 3)
            })
          },
          onCancel() {},
        })
      },
      /** 清理缓存按钮操作 */
      handleRefreshCache() {
        cleanCache().then(response => {
          this.$message.success('刷新成功', 3)
        })
      },
      onExpandCurrent(expandedRowKeys, record) {
        this.initDictName = record.dictName
        this.initDictCode = record.dictCode

        if (expandedRowKeys) {
          this.expandedRowKeys = [record.dictId]
        } else {
          this.expandedRowKeys = []
        }
      },
    },
  }
</script>
