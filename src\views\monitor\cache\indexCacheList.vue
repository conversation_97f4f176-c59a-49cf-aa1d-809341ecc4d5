<template>
  <div>
    <a-row type="flex" :gutter="10">
      <a-col :span="8">
        <a-card :bordered="false" style="height: calc(100vh - 125px)">
          <advance-table
            :columns="columns"
            :data-source="list"
            title="缓存列表"
            :loading="loading"
            :scroll="{ y: 'calc(100vh - 125px)' }"
            rowKey="cacheKey"
            size="middle"
            :isTableConfig="false"
            :isShowSetBtn="false"
            tableKey="monitor-cache-cache-name-list-table"
            @refresh="getList"
            :customRow="onClickRow"
            :format-conditions="true"
          >
            <span slot="indexRender" slot-scope="{ text, record, index }">
              {{ index + 1 }}
            </span>
            <span slot="operation" slot-scope="{ text, record }">
              <a @click="handleClear(record)">删除</a>
            </span>
          </advance-table>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card :bordered="false" style="height: calc(100vh - 125px)">
          <advance-table
            :columns="cacheKeysColumns"
            :data-source="subList"
            title="键名列表"
            :scroll="{ y: 'calc(100vh - 125px)' }"
            :loading="subLoading"
            rowKey="cacheKey"
            size="middle"
            :customRow="onClickSubRow"
            :isTableConfig="false"
            :isShowSetBtn="false"
            tableKey="monitor-cache-cache-key-list-table"
            @refresh="getCacheKeyList"
            :format-conditions="true"
          >
            <span slot="indexSubRender" slot-scope="{ text, record, index }">
              {{ index + 1 }}
            </span>
            <span slot="operation" slot-scope="{ text, record }">
              <a @click.stop="handleClearByKey(record)">删除</a>
            </span>
          </advance-table>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card :bordered="false" style="height: calc(100vh - 125px); overflow-y: auto; overflow-x: hidden">
          <a-form-model ref="form" :model="form" layout="vertical">
            <a-row :gutter="32">
              <a-col :offset="1" :span="22">
                <a-form-model-item label="缓存名称:" prop="cacheName">
                  <a-input v-model="form.cacheName" :readOnly="true" />
                </a-form-model-item>
              </a-col>
              <a-col :offset="1" :span="22">
                <a-form-model-item label="缓存键名:" prop="cacheKey">
                  <a-input v-model="form.cacheKey" :readOnly="true" />
                </a-form-model-item>
              </a-col>
              <a-col :offset="1" :span="22">
                <a-form-model-item label="存活时间:" prop="ttl">
                  <a-input v-model="form.ttl" :readOnly="true" />
                </a-form-model-item>
              </a-col>
              <a-col :offset="1" :span="22">
                <a-form-model-item label="缓存内容:" prop="cacheValue">
                  <a-button
                    type="camera"
                    style="float: right; top: -34px"
                    v-show="isFormattedText == true && form.cacheValue"
                    @click="formattedText()"
                  >
                    <a-icon type="code" />
                    压缩
                  </a-button>
                  <a-button
                    type="camera"
                    style="float: right; top: -34px"
                    v-show="isFormattedText == false && form.cacheValue"
                    @click="formattedText()"
                  >
                    <a-icon type="code" style="transform: rotate(90deg)" />
                    格式化
                  </a-button>
                  <a-textarea
                    v-model="form.cacheValue"
                    :rows="19"
                    :style="form.cacheValue ? 'top:-20px' : ''"
                    style=""
                    :readOnly="true"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>
<script>
  import { listCacheName, clearCache, listCacheKey, clearCacheByKey, getCacheValue } from '@/api/monitor/cache'
  import AdvanceTable from '@/components/pt/table/AdvanceTable'
  export default {
    name: 'CacheName',
    components: {
      AdvanceTable
    },
    data() {
      return {
        list: [],
        // 表格缓存的数据 - 用来点击取消时回显数据
        cacheData: [],
        subList: [],
        selectedRowKeys: [],
        selectedSubRowKeys: [],
        selectedRows: [],
        selectedSubRows: [],
        // 高级搜索 展开/关闭
        advanced: false,
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        subMultiple: true,
        currentSelectCacheId: '',
        selectItem: {},
        selectSubItem: {},
        subLoading: false,
        isFormattedText: false,
        // 表单参数
        form: {},
        total: 0,
        subTotal: 0,
        labelCol: {
          span: 6
        },
        wrapperCol: {
          span: 18
        },
        columns: [
          {
            title: '序号',
            width: '20%',
            align: 'center',
            scopedSlots: {
              customRender: 'indexRender'
            }
          },
          {
            title: '缓存名称',
            dataIndex: 'cacheName',
            ellipsis: true
          },
          {
            title: '备注',
            dataIndex: 'cacheValue',
            ellipsis: true
          },
          {
            title: '操作',
            dataIndex: 'operation',
            width: '80px',
            scopedSlots: {
              customRender: 'operation'
            }
          }
        ],
        cacheKeysColumns: [
          {
            title: '序号',
            width: '20%',
            align: 'center',
            scopedSlots: {
              customRender: 'indexSubRender'
            }
          },
          {
            title: '缓存键名',
            dataIndex: 'cacheKey',
            ellipsis: true
          },
          {
            title: '操作',
            dataIndex: 'operation',
            width: '100px',
            scopedSlots: {
              customRender: 'operation'
            }
          }
        ],
        isNowCacheKey: null
      }
    },
    filters: {},
    created() {
      this.form = {
        cacheName: undefined,
        cacheId: undefined,
        cacheKey: undefined,
        cacheValue: undefined,
        fileType: undefined,
        fileUrl: undefined,
        fileCreateTime: undefined,
        methodName: undefined,
        dtoValue: undefined,
        dto: undefined,
        serviceBean: undefined,
        status: undefined
      }
      this.getList()
    },
    computed: {},
    watch: {
      selectItem(val) {
        /* this.renderStyle(val, 'main') */
        this.getCacheKeyList(val.cacheKey)
      },
      selectSubItem(val) {
        /* this.renderStyle(val, 'sub') */
      }
    },
    methods: {
      /** 查询定时任务列表 */
      async getList() {
        this.loading = true
        const { data: res } = await listCacheName()
        this.list = res
        this.loading = false
        if (this.list.length > 0) {
          this.$nextTick(() => (this.selectItem = this.list[0]))
          this.isNowCacheKey = this.list[0].cacheKey
        }
      },

      /** 删除缓存操作 */
      handleClear(row) {
        var that = this
        this.$confirm({
          title: '确认清除所选中数据的缓存吗?',
          onOk() {
            return clearCache(row.cacheKey).then(() => {
              that.onSelectChange([], [])
              that.getList()
              that.$message.success('缓存清除成功', 3)
            })
          },
          onCancel() {}
        })
      },
      formattedText() {
        this.isFormattedText = !this.isFormattedText
        if (this.isFormattedText) {
          this.form.cacheValue = JSON.stringify(JSON.parse(this.form.cacheValue), null, 4)
        } else {
          this.form.cacheValue = JSON.stringify(JSON.parse(this.form.cacheValue))
        }
      },
      /** 删除缓存key操作 */
      handleClearByKey(row) {
        var that = this
        this.$confirm({
          title: '确认清除所选中数据的缓存吗?',
          onOk() {
            return clearCacheByKey(row.cacheKey).then(() => {
              that.getCacheKeyList(that.isNowCacheKey)
              that.$message.success('缓存清除成功')
            })
          },
          onCancel() {}
        })
      },
      async getCacheKeyList(cacheId) {
        if (typeof cacheId === 'string') {
          if (cacheId === null || cacheId === '' || cacheId === undefined) {
            cacheId = this.currentSelectCacheId
          } else {
            this.currentSelectCacheId = cacheId
          }
        } else {
          cacheId = this.currentSelectCacheId
        }
        // 只有保存的数据才加载字表数据
        this.subLoading = true
        const { data: res } = await listCacheKey(cacheId)
        let newarr = []
        for (let i = 0; i < res.length; i++) {
          const newobj = {
            cacheKey: res[i]
          }
          newarr.push(newobj)
        }
        this.subList = newarr
        this.subLoading = false
      },
      onClickRow(record, index) {
        return {
          on: {
            click: event => {
              this.selectItem = record
              this.isNowCacheKey = record.cacheKey
              this.form = []
            }
          }
        }
      },
      onClickSubRow(record, index) {
        return {
          on: {
            click: event => {
              this.isFormattedText = false
              this.selectSubItem = record
              getCacheValue(record.cacheId, record.cacheKey).then(response => {
                this.form = response.data
              })
            }
          }
        }
      },

      /** 搜索按钮操作 */
      handleQuery() {
        this.getList()
      },
      selectFirstRecord() {
        // 定位选中行到第一条数据
        if (this.list.length > 0) {
          this.selectItem = this.list[0]
        } else {
          // 移除子表数据
          this.subList = []
          this.subTotal = 0
        }
      },
      onSelectChange(selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
        this.single = selectedRowKeys.length !== 1
        this.multiple = !selectedRowKeys.length
      },
      onSelectSubChange(selectedSubRowKeys, selectedSubRows) {
        this.selectedSubRowKeys = selectedSubRowKeys
        this.selectedSubRows = selectedSubRows
        this.subMultiple = !selectedSubRowKeys.length
      }
      /* renderStyle (currentRow, type) { // 增加表格选中行样式
        // 类数组
        const rowEles = document.getElementsByClassName('ant-table-row')
        const rowSelectEles = document.getElementsByClassName(type + '-row-selection')
        let rowList
        if (rowSelectEles.length) {
          rowSelectEles[0].classList.remove(type + '-row-selection')
        }
        if (rowEles.length) {
          rowList = [...rowEles]
          // 这里不用 === 是因为获取的 rowKey 是 String 类型，而给与的原数据 key 为 Number 类型
          // 若要用 === ，事先进行类型转换再用吧
          if (type === 'main') {
          rowList.find(row => row.dataset.rowKey === currentRow.cacheId).classList.add(type + '-row-selection')
          } else if (type === 'sub') {
          rowList.find(row => row.dataset.rowKey === currentRow.cacheKey).classList.add(type + '-row-selection')
          }
        }
      } */
    }
  }
</script>
<style lang="less" scoped>
  /deep/.main-row-selection {
    background-color: #f0f2f5;
  }
  /deep/.sub-row-selection {
    background-color: #f0f2f5;
  }
  /deep/.ant-table-body {
    height: calc(100vh - 225px);
  }
  /deep/.ant-table-placeholder {
    // 将暂无数据提示隐藏，否则没有数据时将表格高度制定导致暂无数据挤到最底下
    display: none;
  }

  /deep/ .ant-table-fixed-header .ant-table-scroll .ant-table-header {
    margin-bottom: -20px !important;
    padding-bottom: 20px !important;
    overflow: auto !important;
  }
  /deep/ .ant-table-fixed-header > .ant-table-content > .ant-table-scroll > .ant-table-body {
    overflow-y: auto !important;
  }
</style>
