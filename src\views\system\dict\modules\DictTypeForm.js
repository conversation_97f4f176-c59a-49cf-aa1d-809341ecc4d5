import AntModal from '@/components/pt/dialog/AntModal'
import { checkDictTypeUnique, getType, saveType, updateType } from '@/api/system/dict/type'
import clickThrottle from '@/utils/clickThrottle'
export default {
  name: 'CreateForm',
  props: {
    statusOptions: {
      type: Array,
      required: true
    }
  },
  components: {
    AntModal
  },
  data() {
    return {
      loading: false,
      formTitle: '',
      // 表单参数
      form: {
        id: undefined,
        dictName: undefined,
        dictCode: undefined,
        status: '0',
        remark: undefined
      },
      open: false,
      rules: {
        dictName: [{ required: true, message: '字典名称不能为空', trigger: 'blur' }],
        dictCode: [{ required: true, message: '字典编码不能为空', trigger: 'blur' }]
      }
    }
  },
  filters: {},
  created() {},
  computed: {},
  watch: {},
  methods: {
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
      this.$emit('close')
    },
    // 表单重置
    reset() {},
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.formTitle = '添加字典类型'
    },
    /** 修改按钮操作 */
    handleUpdate(row, ids) {
      this.reset()
      const dictId = row ? row.dictId : ids
      getType(dictId).then(response => {
        this.form = response.data
        this.open = true
        this.formTitle = '修改【' + this.form.dictName + '】类型'
      })
    },
    /** 提交按钮 */
    submitForm: function () {
      if (!clickThrottle(5000)) return
      this.$refs.form.validate(valid => {
        if (valid) {
          //
          if (this.form.dictId !== undefined) {
            updateType(this.form).then(response => {
              this.$message.success('新增成功', 3)
              this.open = false
              this.$emit('ok')
            })
          } else {
            saveType(this.form).then(response => {
              this.$message.success('修改成功', 3)
              this.open = false
              this.$emit('ok')
            })
          }
        } else {
          return false
        }
      })
    }
  }
}
