<template>
  <a-config-provider :locale="locale">
    <div id="app">
      <router-view />
    </div>
  </a-config-provider>
</template>

<script>
  import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN'
  import { handleIcoCreate } from './utils/setTtitleAndIco.js'

  export default {
    data() {
      return {
        locale: zhCN,
      }
    },
    computed: {},
    created() {
      handleIcoCreate()
    },
    mounted() {
      this.windowResize()
      window.addEventListener('resize', this.windowResize)
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.windowResize)
    },
    methods: {
      windowResize() {
        this.$store.dispatch('onWindowResize')
      },
    },
  }
</script>
