<template>
  <div
    v-if="isRender"
    :class="[
      'upload-content',
      (listType == 'picture' || listType == 'picture-card') && fileList.length < (multiple ? limit : 1) ? '' : 'no-add',
      onlyView ? 'no-add' : '',
    ]"
  >
    <a-upload
      :customRequest="customRequest"
      :list-type="listType"
      :file-list="fileList"
      :beforeUpload="beforeUpload"
      :multiple="multiple"
      @preview="handlePreview"
      @change="handleChange"
      v-bind="$attrs"
    >
      <a-button v-if="listType == 'text'" :disabled="!(fileList.length < (multiple ? limit : 1))">
        <a-icon type="upload" />
        选择文件
      </a-button>

      <div v-if="(listType == 'picture' || listType == 'picture-card') && fileList.length < limit">
        <a-icon :style="{ fontSize: '32px' }" type="plus" />
      </div>

      <a-modal
        v-if="listType == 'picture' || listType == 'picture-card'"
        :visible="previewVisible"
        :footer="null"
        @cancel="handleCancel"
      >
        <img alt="example" style="width: 100%" :src="previewImage" />
      </a-modal>
    </a-upload>
  </div>
</template>

<script>
  import uuid4 from '@/utils/guid.js'

  let Minio = require('minio')
  let stream = require('stream')

  function getBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = error => reject(error)
    })
  }

  function getFileName(url) {
    let arr = url.split('?')[0].split('/')
    const str = arr[arr.length - 1]

    if (arr.length > 1) {
      return decodeURIComponent(str)
    } else {
      return ''
    }
  }

  export default {
    name: 'UploadFile',
    props: {
      multiple: {
        type: Boolean,
        default: true,
      },
      fileUrl: {},
      folderName: { default: '' },
      limit: { default: 100 },
      onlyView: { default: false },
      isRender: { default: true },

      listType: { default: 'text' },
    },
    data: () => ({
      minioClient: null,
      fileList: [],
      previewVisible: false,
      previewImage: '',
      isWatch: true,
    }),

    watch: {
      fileUrl: {
        handler(newVal, oldVal) {
          this.dealEcho(newVal)
        },
        deep: true,
        immediate: true,
      },
    },
    created() {
      const urlObj = new URL(process.env.VUE_APP_MINIO_URL)
      //连接minio文件服务器
      this.minioClient = new Minio.Client({
        endPoint: urlObj.hostname, //对象存储服务的URL
        port: +urlObj.port, //端口号
        useSSL: urlObj.protocol === 'https:', //true代表使用HTTPS
        accessKey: process.env.VUE_APP_MINIO_ACCESSKEY, //账户id
        secretKey: process.env.VUE_APP_MINIO_SECRETKEY, //密码
        partSize: '50M',
      })
    },
    methods: {
      beforeUpload(file, fileList) {
        console.log('beforeUpload', file)
        // if( file.size / 1024 / 1024 < 50) { // 50M限制
        // }
        // if (fileList.length + this.fileList.length > this.limit) {
        //   this.$message.warning(`文件数量大于${this.limit}个，请重新选择`)
        //   return false
        // }
        // this.fileList = []
      },
      customRequest(option) {
        const { file, onProgress, onError, onSuccess } = option
        console.log('custom request option', option)
        //获取文件类型及大小
        const fileName = file.name
        const mineType = file.type
        const fileSize = file.size

        //参数
        let metadata = {
          'content-type': mineType,
          'content-length': fileSize,
        }

        this.fileToBuf(file, (buf, filename) => {
          //定义流
          let bufferStream = new stream.PassThrough()
          //将buffer写入
          bufferStream.end(new Buffer(buf))

          const bucketName = process.env.VUE_APP_MINIO_BUCKET
          const pathName = this.folderName ? `${this.folderName}/${fileName}` : fileName

          let arr = pathName.split('.')

          const pathNameAndUid = `${arr.slice(0, arr.length - 1).join('.')}-uuid-${uuid4()}.${arr[arr.length - 1]}`

          this.minioClient.putObject(bucketName, pathNameAndUid, bufferStream, fileSize, metadata, (error, etag) => {
            if (error == null) {
              const fileUrl = `${process.env.VUE_APP_MINIO_URL}/${bucketName}/${pathNameAndUid}`
              //输出url
              let only = { url: fileUrl, name: pathNameAndUid }
              onSuccess({ status: 200, statusCode: 200, url: only.url }, file)
              console.log('minio upload  success', only)
            }
          })
        })
      },

      // // 删除minIo里的文件
      // removeMinIoFile(url, callback) {
      //   const bucketName = process.env.VUE_APP_MINIO_BUCKET
      //   const fileName = getFileName(url)
      //   const pathName = this.folderName ? `${this.folderName}/${fileName}` : fileName

      //   this.minioClient.removeObject(bucketName, pathName, err => {
      //     if (err == null) {
      //       callback()
      //     }
      //   })
      // },

      // File转arraybuffer
      fileToBuf(file, cb) {
        var fr = new FileReader()
        var filename = file.name

        fr.readAsArrayBuffer(file)
        fr.addEventListener(
          'loadend',
          e => {
            var buf = e.target.result
            cb(buf, filename)
          },
          false
        )
      },

      handleChange(option) {
        const { file, fileList, event } = option

        if (this.multiple) {
          this.$emit(
            'update:fileUrl',
            fileList.map(el => el?.response?.url || el.url)
          )
        } else {
          this.$emit('update:fileUrl', fileList.map(el => el?.response?.url || el.url)[0])
        }

        this.fileList = fileList
      },
      // 预览
      async handlePreview(file) {
        if (this.listType == 'text') {
          if (file.response) {
            window.open(file.response.url)
          } else {
            window.open(file.url)
          }
        } else {
          let arr = file.name.split('.')
          let type = arr[arr.length - 1]
          let imgTypes = ['png', 'jpg', 'jpeg', 'gif']
          if (imgTypes.includes(type.toLocaleLowerCase())) {
            if (!file.url && !file.preview) {
              file.preview = await getBase64(file.originFileObj)
            }
            this.previewImage = file.url || file.preview
            this.previewVisible = true
          } else {
            if (file.response) {
              window.open(file.response.url)
            } else {
              window.open(file.url)
            }
          }
        }
      },
      // 取消
      handleCancel() {
        this.previewVisible = false
      },

      dealEcho(newVal) {
        if (!newVal || newVal?.length == 0) return

        if (this.multiple) {
          let arr = (newVal || [])
            .filter(el => !this.fileList.some(ele => ele?.response?.url == el || ele?.url === el))
            .map(ele => {
              this.fileList.push({
                name: getFileName(ele),
                url: ele,
                uid: uuid4(),
                status: 'done',
              })
            })
        } else {
          if (!newVal || newVal?.length == 0) return
          const file = {
            name: getFileName(newVal),
            url: newVal,
            uid: uuid4(),
            status: 'done',
          }

          this.fileList = [file]
        }
      },
    },
  }
</script>

<style lang="less" scoped>
  @import '~ant-design-vue/lib/style/index';
  .upload-content {
    /deep/ .ant-upload-list-item-name {
      cursor: pointer;
      color: @primary-color;
    }
  }

  .no-add {
    /deep/ .ant-upload-select-picture-card {
      display: none;
    }
  }
</style>
