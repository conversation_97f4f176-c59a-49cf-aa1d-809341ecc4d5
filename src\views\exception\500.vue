<template>
  <a-result status="500" :title="data.code" :sub-title="data.message">
    <template #extra>
      <a-button type="primary" @click="toHome">
        返回
      </a-button>
    </template>
  </a-result>
</template>


<script>
import Cookies from "js-cookie";
  export default {
    name: 'Exception500',
    data(){
      return{
        data:'',
      };
    },
    created(){
      this.errInfo()
    },
    methods: {
      errInfo(){        
        if(Cookies.get("serveErr")){
          this.data =  JSON.parse(Cookies.get("serveErr"))
        }else{
          
          this.data=[{code:"500",message:"对不起，服务器报错。"}]
        }
        
      },
      toHome () {        
        Cookies.remove("serveErr")
        // 异常时，调用登出，来清空历史保留信息
        this.$router.push({ name: 'index' })
      }
    }
  }
</script>
