import request from '@/utils/request'

// 查询角色列表(无条件)
export function listRole() {
  return request({
    url: '/sys/role/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 角色分页查询
export function queryRole(data) {
  return request({
    url: '/sys/role/page',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: data
  })
}

// 查询角色详细
export function getRole(roleId) {
  let data = roleId ? 'roleId=' + roleId : ''
  return request({
    url: '/sys/role/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}

// 新增角色
export function addRole(data) {
  return request({
    url: '/sys/role/add',
    method: 'post',
    data: data
  })
}

// 修改角色
export function updateRole(data) {
  return request({
    url: '/sys/role/update',
    method: 'post',
    data: data
  })
}

// 角色状态修改
export function changeRoleStatus(roleId, isDisabled) {
  let data = 'isDisabled=' + isDisabled
  data += roleId ? '&roleId=' + roleId : ''
  return request({
    url: '/sys/role/switch',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}

// 删除角色
export function delRole(roleIds) {
  let data = roleIds ? '&roleIds=' + roleIds : ''
  return request({
    url: '/sys/role/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}

// 查询最大排序
export function findMaxSort() {
  return request({
    url: '/sys/role/getNextSort',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

//获取角色菜单权限
export function getRoleMenu(roleId) {
  let data = roleId ? '&roleId=' + roleId : ''
  return request({
    url: '/sys/role/menu/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}

export function getNewRoleMenu(roleId) {
  let data = roleId ? '&roleId=' + roleId : ''
  return request({
    url: '/sys/role/menu/listWithCheckInfo',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}

//保存角色菜单权限
export function saveRoleMenu(roleId, menuIds) {
  let data = 'menuIds=' + menuIds
  data += roleId ? '&roleId=' + roleId : ''
  return request({
    url: '/sys/role/menu/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}

// 角色用户分页查询
export function getRoleUserList(data) {
  return request({
    url: '/sys/role/user/page',
    method: 'post',
    data: data
  })
}

//保存角色用户
export function saveRoleUser(roleId, userIds) {
  let data = 'userIds=' + userIds
  data += roleId ? '&roleId=' + roleId : ''
  return request({
    url: '/sys/role/user/add',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}

//删除角色用户
export function delRoleUser(roleId, userIds) {
  let data = 'userIds=' + userIds
  data += roleId ? '&roleId=' + roleId : ''
  return request({
    url: '/sys/role/user/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}

// 校验角色名称唯一性
export function checkRoleNameUnique(data) {
  return request({
    url: '/system/role/checkRoleNameUnique',
    method: 'get',
    params: data
  })
}

// 校验角色名称唯一性
export function checkRoleKeyUnique(data) {
  return request({
    url: '/system/role/checkRoleKeyUnique',
    method: 'get',
    params: data
  })
}

// 角色数据权限
export function dataScope(data) {
  return request({
    url: '/system/role/dataScope',
    method: 'put',
    data: data
  })
}

// 新增角色
export function batchSaveRole(data) {
  return request({
    url: '/system/role/batchSave',
    method: 'post',
    data: data
  })
}

// 给小页授权
export function saveRolePortlet(data) {
  return request({
    url: '/system/role/saveRolePortlet',
    method: 'post',
    data: data
  })
}
