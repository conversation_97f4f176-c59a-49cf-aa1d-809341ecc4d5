<template>
  <div>
    <a-row type="flex" :gutter="10">
      <a-col :span="12">
        <a-card :bordered="false" style="min-height: calc(100vh - 125px)">
          <advance-table
            :columns="columns"
            :data-source="list"
            title="角色管理"
            :loading="loading"
            rowKey="roleId"
            size="middle"
            :scroll="{ y: 'calc(100vh - 245px)' }"
            tableKey="system-role-SysRoleIndex-table"
            @refresh="getList"
            :customRow="onClickRow"
            :rowClassName="rowClassName"
            :format-conditions="true"
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            :pagination="{
              current: queryParam.pageNum,
              pageSize: queryParam.pageSize,
              total: total,
              showSizeChanger: true,
              showLessItems: true,
              showQuickJumper: true,
              pageSizeOptions: ['10', '15', '20', '30', '40'],
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，总计 ${total} 条`,
              onChange: changeSize,
              onShowSizeChange: onShowSizeChange,
            }"
          >
            <div class="table-operations" slot="button">
              <a-input-search
                placeholder="请输入编码/名称"
                v-model="queryParam.keywords"
                style="width: 150px"
                @search="handleQuery"
              />
              <a-button type="primary" size="small" @click="addRow()" v-if="isAdd == 1">
                <a-icon type="plus" />
                新增
              </a-button>
              <a-button type="danger" v-if="!multiple" @click="deleteRow(null, 'batch')">
                <a-icon type="delete" />
                删除
              </a-button>

              <!--新增或修改时显示保存按钮-->
              <a-button type="" v-if="isSave != 0" size="small" @click="batchSaveRole()">
                <a-icon type="save" />
                保存
              </a-button>
            </div>

            <span slot="isDisabled" slot-scope="{ text, record }">
              <a-switch
                slot="actions"
                size="small"
                :style="{
                  'font-size': '10px',
                  'background-color': record.isDisabled == 1 ? '#fc011a' : '#52c41a',
                }"
                :checked="record.isDisabled == 1"
                :disabled="record.roleCode !== GLOBAL.SUPERUSER ? false : true"
                @change="onChangeStatus($event, record)"
              />
              <span :style="{ 'font-size': '12px', color: record.isDisabled == 1 ? '#fc011a' : '#52c41a' }">
                {{ record.isDisabled == 1 ? '停用' : '正常' }}
              </span>
            </span>

            <span slot="roleNameSolt" slot-scope="{ text, record }">
              <a v-if="!record.editable">
                {{ text }}
              </a>
              <a-input placeholder="请输入" v-model="record.roleName" v-if="record.editable" />
            </span>

            <span slot="roleCode" slot-scope="{ text, record }">
              <a v-if="!record.editable || record.roleCode === GLOBAL.SUPERUSER" style="color: black">
                {{ text }}
              </a>
              <a-input
                placeholder="请输入"
                v-model="record.roleCode"
                v-if="record.editable && record.roleCode !== GLOBAL.SUPERUSER"
              />
            </span>
            <span slot="sort" slot-scope="{ text, record }">
              <span v-if="!record.editable">
                {{ text }}
              </span>
              <a-input-number
                placeholder="请输入"
                v-model="record.sort"
                v-if="record.editable"
                :min="0"
                style="width: 100%"
              />
            </span>

            <span slot="operation" slot-scope="{ text, record }">
              <div v-if="record.editable">
                <a @click.stop="cancelEditCell(record)">取消</a>
                <a-divider type="vertical" />
                <a @click.stop="cancelEditCell(record)">删除</a>
              </div>
              <div v-else>
                <a
                  title="编辑"
                  @click.stop="record.roleCode !== GLOBAL.SUPERUSER ? updateRow(record) : ''"
                  :style="{ opacity: record.roleCode !== GLOBAL.SUPERUSER ? 1 : 0.4 }"
                >
                  修改
                </a>
                <a-divider type="vertical" />
                <a
                  @click.stop="record.roleCode !== GLOBAL.SUPERUSER ? deleteRow(record, 'row') : ''"
                  :style="{ opacity: record.roleCode !== GLOBAL.SUPERUSER ? 1 : 0.4 }"
                >
                  删除
                </a>
              </div>
            </span>
          </advance-table>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card :bordered="false" style="min-height: calc(100vh - 125px)">
          <select-user
            v-if="selectItem.roleId"
            selectModel="multi"
            :roleId="selectItem.roleId"
            v-model="selectedUser"
            v-show="false"
            ref="selectUserRef"
          />
          <advance-table
            :columns="roleUserColumns"
            :data-source="subList"
            title="角色用户"
            rowKey="userId"
            :loading="subLoading"
            :scroll="{ y: 'calc(100vh - 245px)' }"
            size="middle"
            tableKey="system-roleuser-SysRoleIndex-table"
            @refresh="getRoleUserListByRoleId"
            :format-conditions="true"
            :row-selection="{ selectedRowKeys: selectedSubRowKeys, onChange: onSelectSubChange }"
            :pagination="{
              current: querySubParam.pageNum,
              pageSize: querySubParam.pageSize,
              pageSizeOptions: ['10', '15', '20', '30', '40'],
              total: subTotal,
              showSizeChanger: true,
              showLessItems: true,
              showQuickJumper: true,
              showTotal: (subTotal, range) => `第 ${range[0]}-${range[1]} 条，总计 ${subTotal} 条`,
              onChange: changeSubSize,
              onShowSizeChange: onShowSizeSubChange,
            }"
          >
            <div class="table-operations" slot="button">
              <!-- <a-input placeholder="请输入姓名" v-model="querySubParam.name" style="width: 120px"/> -->
              <a-input placeholder="请输入姓名/用户名/手机号码" v-model="querySubParam.keywords" style="width: 220px">
                <template #suffix>
                  <a-icon type="close" style="color: rgba(0, 0, 0, 0.45)" @click="resetQueryRoleUser" />
                </template>
              </a-input>
              <a-button style="margin-left: 6px" type="primary" width="40" @click="handleQueryRoleUser">
                <a-icon type="search" />
                查询
              </a-button>
              <a-button type="primary" size="small" @click="handleAddUser()">
                <a-icon type="plus" />
                添加用户
              </a-button>
              <a-button type="danger" v-if="!subMultiple" @click="handleDeleteSub">
                <a-icon type="delete" />
                删除
              </a-button>
            </div>

            <span slot="operation" slot-scope="{ text, record }">
              <a @click.stop="handleDeleteSub(record)">删除</a>
            </span>
          </advance-table>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>
<script>
  import {
    queryRole,
    addRole,
    updateRole,
    delRole,
    getRole,
    findMaxSort,
    changeRoleStatus,
    getRoleUserList,
    saveRoleUser,
    delRoleUser,
  } from '@/api/system/role'
  import AdvanceTable from '@/components/pt/table/AdvanceTable'
  import SelectUser from '@/components/pt/selectUser/SelectUser'
  import { randomUUID } from '@/utils/util'
  import clickThrottle from '@/utils/clickThrottle'
  export default {
    name: 'Role',
    components: {
      AdvanceTable,
      SelectUser,
    },
    data() {
      return {
        list: [],
        initNum: 1,
        // 表格缓存的数据 - 用来点击取消时回显数据
        cacheData: [],
        deleteData: [], // 可编辑表格待删除数据，数据库已存在数据界面假删除，保存到该集合，最终请求数据库删除
        subList: [],
        selectedRowKeys: [],
        selectedSubRowKeys: [],
        selectedRows: [],
        selectedSubRows: [],
        selectedUser: '',
        // 高级搜索 展开/关闭
        advanced: false,
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        subMultiple: true,
        currentSelectRoleId: '',
        selectItem: {},
        ids: [],
        names: [],
        subIds: [], // 子表选择id集合
        subNames: [],
        rolesort: 0, //获取最新排序号
        loading: false,
        subLoading: false,
        isSave: 0, //默认为0，不显示；新增或修改时显示
        isReset: 1, //默认为1，点重置时为2
        isAdd: 1, //默认为1，点新增时为2
        editParam: null, //默认修改参数
        total: 0,
        subTotal: 0,
        // 日期范围
        dateRange: [],
        labelCol: {
          span: 6,
        },
        wrapperCol: {
          span: 18,
        },
        queryParam: {
          pageNum: 1,
          isDisabled: null,
          keywords: '',
          pageSize: 15,
          sort: [],
        },
        querySubParam: {
          pageNum: 1,
          pageSize: 15,
          keywords: '',
          name: '',
          roleId: '',
          sort: [],
        },
        addModalRefName: 'addModal', // 添加弹窗ref名称
        columns: [
          {
            title: '角色名称',
            dataIndex: 'roleName',
            ellipsis: true,
            scopedSlots: {
              customRender: 'roleNameSolt',
            },
          },
          {
            title: '角色编码',
            dataIndex: 'roleCode',
            width: '150px',
            ellipsis: true,
            scopedSlots: {
              customRender: 'roleCode',
            },
          },
          {
            title: '排序号',
            dataIndex: 'sort',
            align: 'center',
            scopedSlots: {
              customRender: 'sort',
            },
          },
          {
            title: '状态',
            dataIndex: 'isDisabled',
            scopedSlots: {
              customRender: 'isDisabled',
            },
            align: 'center',
          },
          {
            title: '操作',
            dataIndex: 'operation',
            width: '100px',
            scopedSlots: {
              customRender: 'operation',
            },
          },
        ],
        roleUserColumns: [
          {
            title: '用户姓名',
            dataIndex: 'name',
            ellipsis: true,
          },
          {
            title: '用户名',
            dataIndex: 'username',
            ellipsis: true,
          },
          {
            title: '手机号',
            dataIndex: 'mobile',
            ellipsis: true,
          },
          {
            title: '操作',
            dataIndex: 'operation',
            width: '100px',
            scopedSlots: {
              customRender: 'operation',
            },
          },
        ],
      }
    },
    filters: {},
    created() {
      this.getList()
    },
    computed: {},
    watch: {
      selectedUser(val) {
        let userIds = val.ids
        saveRoleUser(this.currentSelectRoleId, userIds).then(response => {
          this.$message.success('添加成功', 3)
          this.getRoleUserListByRoleId(this.currentSelectRoleId)
        })
      },
      selectItem: {
        handler(newVal, oldVal) {
          if (newVal) {
            this.selectItem = newVal
            this.getRoleUserListByRoleId(newVal.roleId)
          }
        },
        immediate: true,
        deep: true,
      },
    },
    methods: {
      /** 查询定时任务列表 */
      getList() {
        this.showAddModal = false
        this.showEditModal = false
        this.showDataScopeModal = false
        this.loading = true
        queryRole(this.queryParam).then(response => {
          this.list = response.data.data
          this.list.map(item => {
            item.operation = item.remark
          })
          this.total = response.data.total
          this.loading = false
          if (this.list.length > 0 && this.initNum == 1) {
            this.$nextTick(() => (this.selectItem = this.list[0]))
            this.getRoleUserListByRoleId(this.list[0].roleId)
          }
        })
      },
      getRoleUserListByRoleId(roleId) {
        this.subLoading = true
        if (!roleId) {
          return
        } else {
          this.currentSelectRoleId = roleId
          roleId = typeof roleId == 'string' ? '10000' : roleId
          this.querySubParam.roleId = roleId
        }

        if (this.isAdd == 1) {
          getRoleUserList(this.querySubParam).then(response => {
            this.subList = response.data.data
            this.subTotal = response.data.total
            this.subLoading = false
          })
        } else if (this.isAdd == 2) {
          this.subList = []
          this.subLoading = false
          this.subTotal = 0
        }
        this.subIds = []
        this.selectedSubRowKeys = []
        this.subMultiple = true
      },
      onClickRow(record, index) {
        return {
          on: {
            click: event => {
              if (event.target !== undefined && event.target.localName === 'td') {
                if (this.validaData()) {
                  this.selectItem = record
                }
              }
            },
          },
        }
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      handleQueryRoleUser() {
        this.querySubParam.pageNum = 1
        this.getRoleUserListByRoleId(this.currentSelectRoleId)
      },
      resetQueryRoleUser() {
        this.isReset = 2
        this.querySubParam = {
          pageNum: 1,
          pageSize: 10,
          keywords: '',
          name: '',
          roleId: this.currentSelectRoleId,
          sort: [],
        }
        this.getRoleUserListByRoleId(this.currentSelectRoleId)
      },
      async addRow() {
        this.isAdd = 2
        this.isSave = 1

        const flag = this.validaData()

        if (flag) {
          /** 获取最大编号 */
          await findMaxSort().then(response => {
            this.rolesort = response.data
          })
          // 角色添加行方法
          const id = randomUUID()
          const newRow = {
            roleId: 'newRow-' + id, // newRow 表示该行是新增的，提交成功后 key替换为数据库ID
            roleName: '',
            roleCode: '',
            sort: this.rolesort,
            remark: '',
            editable: true,
          }
          // 关闭所有已打卡行编辑
          this.closeEditCell(this.list)
          this.list.unshift(newRow)
          this.$nextTick(() => (this.selectItem = newRow))
        }
      },
      updateRow(record) {
        this.isSave = 2
        this.editParam = record
        // 角色编辑行方法
        const newData = [...this.list]
        this.closeEditCell(newData)
        record.handleType = 'edit'
        record.editable = true
        this.list = newData
        this.$nextTick(() => (this.selectItem = record))
      },
      cancelEditCell(record) {
        if (this.validaData()) {
          // 取消行编辑
          const newData = [...this.list]
          record.editable = false
          this.isSave = 0
          if (this.isAdd == 2) {
            this.list = newData.slice(1)
            this.isAdd = 1
          } else {
            this.list = newData
          }
        }
      },

      realDeleteRow(record, type) {
        // console.log('del row',this.GLOBAL.SUPERUSER,record)
        if (record?.roleCode == this.GLOBAL.SUPERUSER) {
          this.$message.info('管理员不允许删除！')
        } else {
          const newData = [...this.list]
          const ids = record?.roleId || this.ids
          // record?.handleType = 'delete'
          delRole(ids).then(res => {
            this.$message.success('删除成功', 3)
            this.getList()
          })

          // const target = newData.filter((item) => record.roleId !== item.roleId);
          // if (target) {
          //   this.list = target
          // }
          // if (type !== 'batch') {
          //   this.selectFirstRecord()
          // }
        }
      },
      deleteRow(record, type) {
        // const roleId = record.roleId || this.ids
        const that = this
        if (type !== 'batch') {
          this.$confirm({
            title: '删除角色会删除相关的菜单，用户关系等，确认删除吗?',
            onOk() {
              that.realDeleteRow(record, type)
            },
            onCancel() {},
          })
        } else {
          this.$confirm({
            title: '删除角色会删除相关的菜单，用户关系等，确认删除吗?',
            onOk() {
              that.realDeleteRow(record, type)
            },
            onCancel() {},
          })
        }
      },

      selectFirstRecord() {
        // 定位选中行到第一条数据
        if (this.list.length > 0) {
          this.selectItem = this.list[0]
        } else {
          // 移除子表数据
          this.subList = []
          this.subTotal = 0
        }
      },
      /** 更新角色状态 (0-正常 1-停用)**/
      onChangeStatus(e, record) {
        var that = this
        const roleId = record.roleId
        let isRoleDisabled = 0
        let recordIsDisabled = !record.isDisabled
        isRoleDisabled = recordIsDisabled ? 1 : 0
        let isDisabledName = isRoleDisabled == 1 ? '停用' : '启用'
        this.$confirm({
          title: '是否"' + isDisabledName + '"所选中数据?',
          content: '当前选中的数据',
          onOk() {
            return changeRoleStatus(roleId, isRoleDisabled).then(res => {
              if (res.code == 200 && res.success == true) {
                that.getList()
              } else if (res.code != 200) {
                that.$message.success('"' + isDisabledName + '"失败:', res.message)
              } else {
              }
            })
          },
          onCancel() {},
        })
      },
      batchSaveRole() {
        if (!clickThrottle(5000)) return
        // 保存角色数据
        if (this.isSave == 1) {
          let addParam = this.list[0]
          if (this.list.length === 0) {
            this.$message.info('没有可保存的数据！')
          }
          addRole(addParam).then(response => {
            this.$message.success('保存成功', 3)
            this.isAdd = 1
            this.isSave = 0
            this.getList()
          })
        } else if (this.isSave == 2) {
          if (this.list.length === 0) {
            this.$message.info('没有可保存的数据！')
          }
          updateRole(this.editParam).then(response => {
            this.$message.success('保存成功', 3)
            this.isSave = 0
            this.getList()
          })
        }
      },
      validaData() {
        let flag = true
        this.list.forEach(item => {
          if (item.handleType !== 'undefined' && (item.handleType === 'add' || item.handleType === 'edit')) {
            if (item.roleName === '' || item.roleName === 'undefined' || item.roleName === null) {
              this.$message.info('请先维护角色名称为空的数据！')
              flag = false
            }
            if (flag && (item.roleKey === '' || item.roleKey === 'undefined' || item.roleKey === null)) {
              this.$message.info('请先维护角色编码为空的数据！')
              flag = false
            }
          }
        })
        return flag
      },
      closeEditCell(data) {
        // 关闭所有打开的可编辑行
        const list = data.filter(item => item.editable === true)
        list.forEach(item => {
          item.editable = false
        })
      },
      handleAddUser() {
        this.$nextTick(() => this.$refs.selectUserRef.showSelectUser())
      },
      onShowSizeChange(pageNum, pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      onShowSizeSubChange(pageNum, pageSize) {
        this.querySubParam.pageSize = pageSize
        this.getRoleUserListByRoleId(this.currentSelectRoleId)
      },
      onSizeChange(pageNum, size) {
        this.queryParam.pageNum = 1
        this.queryParam.pageSize = size
        this.getList()
      },
      changeSize(pageNum, pageSize) {
        this.initNum = 2
        this.queryParam.pageNum = pageNum
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      changeSubSize(pageNum, pageSize) {
        this.querySubParam.pageNum = pageNum
        this.querySubParam.pageSize = pageSize
        this.getRoleUserListByRoleId(this.currentSelectRoleId)
      },
      onSelectChange(selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
        this.ids = this.selectedRows.map(item => item.roleId)
        this.single = selectedRowKeys.length !== 1
        this.multiple = !selectedRowKeys.length
      },
      onSelectSubChange(selectedSubRowKeys, selectedSubRows) {
        this.selectedSubRowKeys = selectedSubRowKeys
        this.selectedSubRows = selectedSubRows
        this.subIds = this.selectedSubRows.map(item => item.userId)
        this.subNames = this.selectedSubRows.map(item => item.name)
        this.subMultiple = !selectedSubRowKeys.length
      },
      /** 删除按钮操作 */
      handleDeleteSub(row) {
        var that = this
        const ids = row.userId || this.subIds
        const names = row.name || this.subNames
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return delRoleUser(that.currentSelectRoleId, ids).then(() => {
              that.subMultiple = true
              that.onSelectSubChange([], [])
              that.getRoleUserListByRoleId(that.currentSelectRoleId)
              that.$message.success('删除成功', 3)
            })
          },
          onCancel() {},
        })
      },
      // 选中行样式
      rowClassName(row) {
        let flag = false
        if (row.roleId == this.selectItem.roleId) {
          flag = true
        }
        return flag ? 'changeBgcolor' : ''
      },
    },
  }
</script>
<style lang="less" scoped>
  /deep/.changeBgcolor {
    background-color: #f0f5ff !important;
  }
  /deep/ .ant-table-fixed-header .ant-table-scroll .ant-table-header {
    margin-bottom: -20px !important;
    padding-bottom: 20px !important;
    overflow: auto !important;
  }
  /deep/ .ant-table-fixed-header > .ant-table-content > .ant-table-scroll > .ant-table-body {
    overflow-y: auto !important;
  }
</style>
