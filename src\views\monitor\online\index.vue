<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="姓名">
        <a-input v-model="queryParam.name" placeholder="请输入姓名" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <a-form-item label="登录名称">
        <a-input
          v-model="queryParam.username"
          placeholder="请输入登录名称"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="在线用户"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          :tablePage="false"
        ></VxeTable>
      </template>
    </VxeTableForm>
  </div>
</template>

<script>
  import { list, forceLogout } from '@/api/monitor/online'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'

  export default {
    name: 'Online',
    components: {
      VxeTable,
      VxeTableForm
    },
    data() {
      return {
        list: [],
        loading: false,
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        total: 0,
        // 非多个禁用
        multiple: true,
        queryParam: {
          /* pageNum: 1,
        pageSize: 10,
        ipaddr: undefined,
        userName: undefined */
          appId: '',
          name: '',
          userId: null,
          username: ''
        },
        columns: [
          { type: 'seq', title: '序号', align: 'center', width: 50 },
          {
            title: '会话编号',
            field: 'token',
            showOverflow: 'tooltip',
            align: 'center'
          },
          {
            title: '登录名称',
            field: 'username',
            align: 'center'
          },
          /* {
          title: '部门名称',
          dataIndex: 'deptName',
          align: 'center'
        }, */
          {
            title: '姓名',
            field: 'name',
            align: 'center'
          },
          {
            title: '登录地址',
            field: 'ipAddress',
            showOverflow: 'tooltip',
            align: 'center'
          },
          {
            title: '登录地点',
            field: 'loginLocation',
            align: 'center'
          },
          {
            title: '浏览器',
            field: 'browser',
            align: 'center'
          },
          {
            title: '操作系统',
            field: 'os',
            align: 'center'
          },
          {
            title: '登录时间',
            field: 'loginTime',
            width: 180,
            align: 'center'
          },
          {
            title: '操作',
            field: 'operate',
            width: 100,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a-popconfirm
                      ok-text='是'
                      cancel-text='否'
                      onConfirm={() => this.confirmHandleForceLogout(row)}
                      onCancel={() => this.cancelHandleForceLogout(row)}
                    >
                      <span slot='title'>
                        确认强退
                        <b>{row.userName}</b>
                        的用户吗?
                      </span>
                      <a style='color: #2f54eb'>强退</a>
                    </a-popconfirm>
                  </span>
                )
              }
            }
          }
        ]
      }
    },
    filters: {},
    created() {
      this.getList()
    },
    computed: {},
    watch: {},
    methods: {
      /** 查询登录日志列表 */
      async getList() {
        this.loading = true
        const { data: res } = await list(this.queryParam)
        this.list = res
        this.loading = false
        /* list(this.queryParam).then(response => {
          this.list = response.rows
          this.total = response.total
          this.loading = false
        }
      ) */
      },
      /** 搜索按钮操作 */
      handleQuery() {
        /* this.queryParam.pageNum = 1 */
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = []
        this.queryParam = {
          appId: '',
          name: '',
          userId: null,
          username: ''
        }
        this.handleQuery()
      },
      /* onShowSizeChange (current, pageSize) {
      this.queryParam.pageSize = pageSize
      this.getList()
    }, */
      /*  changeSize (current, pageSize) {
      this.queryParam.pageNum = current
      this.queryParam.pageSize = pageSize
      this.getList()
    }, */
      /** 强退按钮操作 */
      async confirmHandleForceLogout(row) {
        const { message: res } = await forceLogout(row.token)
        if (res !== '成功') {
          this.$message.error('发生异常')
        }
        this.$message.success('已强退')
        /* forceLogout(row.token)
      .then(() => {
        this.getList()
        this.$message.success(
          '已强退',
          3
        )
      }).catch(function () {
        this.$message.error(
          '发生异常',
          3
        )
      }) */
      },
      cancelHandleForceLogout(row) {}
    }
  }
</script>
