import request from '@/utils/request'

// 查询岗位列表
// export function listPost (query) {
//   return request({
//     url: '/system/post/page',
//     method: 'get',
//     params: query
//   })
// }

// 查询岗位列表
export function listPost(query) {
  return request({
    url: '/sys/post/list',
    method: 'post'
  })
}

// 查询岗位分页列表
export function listPagePost(query) {
  return request({
    url: '/sys/post/page',
    method: 'post',
    data: query
  })
}


// 查询岗位详细
// export function getPost(id) {
//   return request({
//     url: '/system/post/' + id,
//     method: 'get'
//   })
// }

// 查询岗位详细
export function getPost(id) {
  return request({
    url: '/sys/post/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: 'postId=' + id
  })
}

// 新增岗位
// export function savePost(data) {
//   return request({
//     url: '/system/post',
//     method: 'post',
//     data: data
//   })
// }

// 新增岗位
export function savePost(data) {
  return request({
    url: '/sys/post/add',
    method: 'post',
    data: data
  })
}

// 更新岗位
export function upDatePost(data) {
  return request({
    url: '/sys/post/update',
    method: 'post',
    data: {
      postCode: data.postCode,
      postId: data.postId,
      postName: data.postName,
      remark: data.remark,
      sort: data.sort
    }
  })
}

// 字典类型状态:启用-停用
export function dictPostStatus(isDisabled, postId) {
  let data = 'isDisabled=' + isDisabled
  data += postId ? '&postId=' + postId : ''
  return request({
    url: '/sys/post/switch',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  })
}

// 删除岗位
// export function delPost(id) {
//   return request({
//     url: '/system/post/' + id,
//     method: 'delete'
//   })
// }

// 删除岗位
export function delPost(id) {
  return request({
    url: '/sys/post/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: 'postIds=' + id
  })
}

// 导出岗位
export function exportPost(query) {
  return request({
    url: '/system/post/export',
    method: 'get',
    params: query
  })
}

// 岗位编码唯一校验
export function checkPostCodeUnique(data) {
  return request({
    url: 'system/post/checkPostCodeUnique',
    method: 'get',
    params: data
  })
}

// 岗位名称唯一校验
export function checkPostNameUnique(data) {
  return request({
    url: 'system/post/checkPostNameUnique',
    method: 'get',
    params: data
  })
}

// 查询岗位最大排序
// export function findMaxSort() {
//   return request({
//     url: '/system/post/findMaxSort',
//     method: 'get'
//   })
// }

// 查询岗位最大排序
export function findMaxSort() {
  return request({
    url: '/sys/post/getNextSort',
    method: 'post'
  })
}