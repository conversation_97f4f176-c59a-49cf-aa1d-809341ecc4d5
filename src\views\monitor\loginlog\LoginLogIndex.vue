<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="应用">
        <a-select placeholder="请选择应用" v-model="queryParam.appId" style="width: 100%" allow-clear>
          <a-select-option v-for="(d, index) in menuList" :key="index" :value="d.menuCode">
            {{ d.menuName }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="用户名">
        <a-input v-model="queryParam.username" style="width: 100%" allow-clear />
      </a-form-item>

      <a-form-item label="登录时间">
        <a-range-picker
          style="width: 100%"
          v-model="dateRange"
          valueFormat="YYYY-MM-DD"
          format="YYYY-MM-DD"
          allow-clear
        />
      </a-form-item>

      <a-form-item label="登录地址">
        <a-input
          v-model="queryParam.loginLocation"
          placeholder="请输入登录地址"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="登录日志"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @selectChange="selectChange"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="danger" @click="handleClean">
              <a-icon type="delete" />
              清空
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete">
              <a-icon type="delete" />
              删除
            </a-button>
            <!-- <a-button @click="handleUnlock" :disabled="single">
              <a-icon type="delete" />
              解锁
            </a-button> -->
          </div>
        </VxeTable>
      </template>
    </VxeTableForm>
  </div>
</template>
<script>
  import { list, delLoginLog, cleanLoginLog, exportLoginLog, unlockLoginLog } from '@/api/monitor/loginLog'
  import { getMenuList } from '@/api/system/menu'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  export default {
    name: 'LoginLog',
    components: {
      VxeTable,
      VxeTableForm,
    },
    data() {
      return {
        list: [],
        selectedRowKeys: [],
        selectedRows: [],
        // 高级搜索 展开/关闭
        advanced: false,
        // 非单个禁用
        single: true,
        // 非多个禁用
        isChecked: true,
        // 选择用户名
        selectName: '',
        ids: [],
        menuList: [],
        loading: false,
        sunloading: false,
        total: 0,
        // 状态数据字典
        statusOptions: [],
        // 日期范围
        dateRange: [],
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        queryParam: {
          appId: '',
          loginLocation: '',
          loginTimeBegin: '',
          loginTimeEnd: '',
          name: '',
          pageNum: 1,
          pageSize: 10,
          sort: [],
          userId: null,
          username: '',
        },
        addModalRefName: 'addModal', // 添加弹窗ref名称
        columns: [
          { type: 'checkbox', width: 30, align: 'center' },
          { type: 'seq', title: '序号', align: 'center', width: 50 },
          {
            title: '用户名',
            field: 'username',
            align: 'center',
          },
          {
            title: '姓名',
            field: 'name',
            align: 'center',
          },
          {
            title: '应用',
            field: 'appId',
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => this.typeFormat(row),
            },
          },
          {
            title: '登录地址',
            field: 'ipAddress',
            align: 'center',
          },
          {
            title: '登录地点',
            field: 'loginLocation',
            align: 'center',
          },
          {
            title: '登录方式',
            field: 'loginMethodName',
            align: 'center',
          },
          {
            title: '浏览器',
            field: 'browser',
            align: 'center',
          },
          {
            title: '操作系统',
            field: 'os',
            align: 'center',
          },
          {
            title: '登录时间',
            field: 'loginTime',
            align: 'center',
          },
        ],
      }
    },
    filters: {},
    created() {
      this.getList()
      getMenuList().then(response => {
        this.menuList = response.data
      })
    },
    computed: {},
    watch: {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = !pageSize ? 9 : pageSize
        this.getList()
      },
      onSizeChange(current, size) {
        this.queryParam.pageNum = 1
        this.queryParam.pageSize = size
        this.getList()
      },
      /** 查询定时任务列表 */
      getList() {
        this.loading = true
        this.selectChange({ records: [] })

        if (this.dateRange !== null && this.dateRange !== '' && this.dateRange.length === 2) {
          this.queryParam.loginTimeBegin = this.dateRange[0]
          this.queryParam.loginTimeEnd = this.dateRange[1]
        }
        list(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = []
        this.queryParam = {
          pageNum: 1,
          pageSize: 10,
          loginLocation: '',
          username: '',
          appId: '',
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.id)
        this.names = valObj.records.map(item => item.username)
        this.isChecked = !!valObj.records.length
        this.single = valObj.records.length !== 1
        if (!this.single) {
          this.selectName = valObj.records.map(item => item.username)
        }
      },

      typeFormat(row) {
        return this.selectMenuLabel(this.menuList, row.appId)
      },
      toggleAdvanced() {
        this.advanced = !this.advanced
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row.id ? [row.id] : this.ids
        const names = row.username || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return delLoginLog({ ids: ids.join(',') }).then(() => {
              that.selectChange({ records: [] })
              that.getList()
              that.$message.success('删除成功', 3)
            })
          },
          onCancel() {},
        })
      },
      /** 清空按钮操作 */
      handleClean() {
        var that = this
        let cleanData
        if (this.dateRange.length) {
          cleanData = {
            endDate: this.dateRange[0],
            startDate: this.dateRange[1],
          }
        }
        this.$confirm({
          title: '是否确认清空?',
          content: '此操作将会清空所有登录日志数据项',
          onOk() {
            return cleanLoginLog(cleanData).then(() => {
              that.selectChange({ records: [] })
              that.getList()
              that.$message.success('清空成功', 3)
            })
          },
          onCancel() {},
        })
      },
      /** 解锁按钮操作 */
      handleUnlock() {
        var that = this
        const username = this.selectName
        that.$confirm({
          title: '是否确认解锁用户"' + username + '"?',
          onOk() {
            return unlockLoginLog(username).then(() => {
              that.selectChange({ records: [] })
              that.getList()
              that.$message.success('用户' + username + '解锁成功', 3)
            })
          },
          onCancel() {},
        })
      },
      /** 导出按钮操作 */
      handleExport() {
        var that = this
        this.$confirm({
          title: '是否确认导出?',
          content: '此操作将导出当前条件下所有数据而非选中数据',
          onOk() {
            return exportLoginLog(that.queryParam).then(response => {
              that.download(response.msg)
              that.$message.success('导出成功', 3)
            })
          },
          onCancel() {},
        })
      },
    },
  }
</script>
