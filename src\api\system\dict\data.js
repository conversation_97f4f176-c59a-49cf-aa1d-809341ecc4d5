import request from '@/utils/request'

// 获取字典下拉选项
export function getOptions(dictCode) {
  return request({
    url: '/sys/dict/getOptions',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { dictCode },
  })
}

// 查询字典数据列表
export function listData(dictCode) {
  let data = dictCode ? 'dictCode=' + dictCode : ''
  return request({
    url: '/sys/dict/getDictData',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  })
}

// 查询字典数据详细
export function getData(dictDataId) {
  let data = dictDataId ? 'dictDataId=' + dictDataId : ''
  return request({
    url: '/sys/dict/data/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  })
}

// 新增字典数据
export function saveData(data) {
  return request({
    url: '/sys/dict/data/add',
    method: 'post',
    data: data,
  })
}
// 编辑字典数据
export function updateData(data) {
  return request({
    url: '/sys/dict/data/update',
    method: 'post',
    data: data,
  })
}

// 删除字典数据
export function delData(dictDataIds) {
  let data = dictDataIds ? 'dictDataIds=' + dictDataIds : ''
  return request({
    url: '/sys/dict/data/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  })
}

// 启用-停用字典数据
export function dictDataStatus(isDisabled, dictDataId) {
  let data = 'isDisabled=' + isDisabled
  data += dictDataId ? '&dictDataId=' + dictDataId : ''
  return request({
    url: '/sys/dict/data/switch',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  })
}

// 获取排序号findMaxSort
export function getNextSort(dictCode) {
  let data = dictCode ? 'dictCode=' + dictCode : ''
  return request({
    url: '/sys/dict/data/getNextSort',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  })
}

// 根据字典类型查询字典数据信息
export function getDicts(dictCode) {
  let data = dictCode ? 'dictCode=' + dictCode : ''
  return request({
    url: '/sys/dict/getDictData',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  })
}

// 根据字典类型查询字典数据信息
export function getAllDicts(dictType) {
  return request({
    url: '/system/dict/data/all/type/' + dictType,
    method: 'get',
  })
}

// 导出字典数据
export function exportData(query) {
  return request({
    url: '/system/dict/data/export',
    method: 'get',
    params: query,
  })
}

// 查询字典类型列表
export function checkDictDataValueUnique(data) {
  return request({
    url: 'system/dict/data/checkDictDataValueUnique',
    method: 'get',
    params: data,
  })
}
