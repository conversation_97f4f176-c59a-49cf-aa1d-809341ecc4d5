<template>
  <ant-modal
    :visible="open"
    :loading="modalLoading"
    :modal-title="formTitle"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="680"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model ref="form" :model="form" layout="vertical">
          <a-form-model-item label="" class="vue-json-editor">
            <vue-json-editor
              v-model="form.content"
              :showBtns="false"
              mode="code"
              lang="ch"
              :expandedOnStart="true"
              @json-change="onJsonChange"
              @json-save="onJsonSave"
              @has-error="onJsonError"
            ></vue-json-editor>
          </a-form-model-item>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <CopyToClipboard
        :text="JSON.stringify(this.form.content)"
        @copy="() => this.$message.success('已复制到剪贴板', 3)"
        v-if="action === 'export'"
      >
        <a-button type="primary" style="margin-right: 10px">复制</a-button>
      </CopyToClipboard>

      <a-button type="primary" @click="doImport" :loading="loading" v-else>导入</a-button>
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script>
  import AntModal from '@/components/pt/dialog/AntModal'
  import vueJsonEditor from 'vue-json-editor'
  import CopyToClipboard from 'vue-copy-to-clipboard'

  export default {
    name: 'ImportExportJsonModal',
    props: [],
    components: { AntModal, vueJsonEditor, CopyToClipboard },
    data() {
      return {
        action: 'export', //export|import
        open: false,
        loading: false,
        modalLoading: false,
        formTitle: '',
        // 表单参数
        form: {
          id: null,
          content: ''
        },
        rules: {}
      }
    },
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 新增按钮操作 */
      showExport(record) {
        this.showWindow(record)
      },
      showImport(record) {
        this.showWindow(record)
      },
      showWindow(record) {
        this.open = true
        this.action = record.action
        this.formTitle = record.title ? record.title : action === 'export' ? '导出' : '导入'
        this.form.id = record.id
      },
      setContent(content) {
        this.form.content = content
      },
      doImport() {
        let that = this
        this.$refs.form.validate(valid => {
          if (valid) {
            that.loading = true
            that.$emit('sendImportRequest', { id: that.form.id, content: that.form.content })
          } else {
            return false
          }
        })
      },
      setLoading(loading) {
        this.loading = loading
      },
      setModalLoading(modalLoading) {
        console.log('setModalLoading ' + modalLoading)
        this.modalLoading = modalLoading
      },
      handleImportResult(res) {
        if (res.code == 200) {
          this.$message.success(`导入成功，一共导入${res.data}数据`, 3)
          this.open = false
          this.$emit('afterImport')
          this.loading = false
        }
      },
      onJsonChange(val) {
        // console.log(val)
      },
      onJsonSave(val) {
        // console.log(val)
      },
      onJsonError(val) {
        this.$message.error('请输入正确的JSON格式')
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .jsoneditor-vue {
    width: 100%;
    height: 470px;
  }
  /deep/ .jsoneditor-poweredBy,
  /deep/ .jsoneditor-search {
    display: none !important;
  }
  /deep/ .jsoneditor-modes .jsoneditor-contextmenu-root .jsoneditor-contextmenu ul li button.jsoneditor-selected,
  /deep/ .jsoneditor-modes .jsoneditor-contextmenu-root .jsoneditor-contextmenu ul li button.jsoneditor-selected:hover,
  /deep/ .jsoneditor-modes .jsoneditor-contextmenu-root .jsoneditor-contextmenu ul li button.jsoneditor-selected:focus {
    background-color: #4f81f7;
  }
</style>
