import AntModal from '@/components/pt/dialog/AntModal'
import { checkDictDataValueUnique, getData, saveData, updateData, getNextSort } from '@/api/system/dict/data'
import clickThrottle from '@/utils/clickThrottle'
export default {
  name: 'CreateDataForm',
  props: {
    dictCode: {
      type: String,
      required: true
    },
    statusOptions: {
      type: Array,
      required: true
    },
    title: String
  },
  components: {
    AntModal
  },
  data() {
    return {
      loading: false,
      formTitle: '',
      // 表单参数
      form: {
        dictDataId: undefined,
        dictCode: undefined,
        dictKey: undefined,
        dictValue: undefined,
        option1: undefined,
        option2: undefined,
        option3: undefined,
        option4: undefined,
        option5: undefined,
        sort: 0,
        isDisabled: '0',
        remark: undefined
      },
      open: false,
      rules: {
        dictCode: [{ required: true, message: '字典编码不能为空', trigger: 'blur' }],
        dictKey: [{ required: true, message: '字典键不能为空', trigger: 'blur' }],
        dictValue: [{ required: true, message: '字典值不能为空', trigger: 'blur' }],
        sort: [{ required: true, message: '数据顺序不能为空', trigger: 'blur' }]
      }
    }
  },
  filters: {},
  created() {},
  computed: {},
  watch: {},
  methods: {
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
      this.$emit('close')
    },
    // 表单重置
    reset() {},
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      /** 获取最大编号 */
      getNextSort(this.dictCode).then(response => {
        this.form.sort = response.data
        this.open = true
        this.formTitle = '添加【' + this.title + '】子表数据'
        this.form.dictCode = this.dictCode
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const dictDataId = row.dictDataId
      getData(dictDataId).then(response => {
        this.form = response.data
        this.open = true
        this.formTitle = '修改【' + this.title + '】子表数据'
      })
    },
    /** 提交按钮 */
    submitForm: function () {
      if (!clickThrottle(5000)) return
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.form.dictDataId !== undefined) {
            updateData(this.form).then(response => {
              this.$message.success('修改成功', 3)
              this.open = false
              this.$emit('ok')
            })
          } else {
            saveData(this.form).then(response => {
              this.$message.success('新增成功', 3)
              this.open = false
              this.$emit('ok')
            })
          }
        } else {
          return false
        }
      })
    }
  }
}
