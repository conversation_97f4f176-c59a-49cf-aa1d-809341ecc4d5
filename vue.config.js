const path = require('path')
const webpack = require('webpack')
const GitRevisionPlugin = require('git-revision-webpack-plugin')
const GitRevision = new GitRevisionPlugin()
const buildDate = JSON.stringify(new Date().toLocaleString())
const createThemeColorReplacerPlugin = require('./config/plugin.config')
const CompressionWebpackPlugin = require('compression-webpack-plugin')
const productionGzipExtensions = ['js', 'css']

function resolve(dir) {
  return path.join(__dirname, dir)
}

// check Git
function getGitHash() {
  try {
    return GitRevision.version()
  } catch (e) {}
  return 'unknown'
}

const isProd = process.env.NODE_ENV === 'production'

// 打包时配置了环境变量
// 后端地址
process.env.VUE_APP_BASE_API = process.env.npm_config_base_api || process.env.VUE_APP_BASE_API
// 单点登录
process.env.VUE_APP_CAS_URL = process.env.npm_config_cas_url || process.env.VUE_APP_CAS_URL
// ico
process.env.VUE_APP_FAVICON = process.env.npm_config_favicon || 'base'

// vue.config.js
const vueConfig = {
  configureWebpack: {
    // webpack plugins
    plugins: [
      // Ignore all locale files of moment.js
      new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/),
      new webpack.DefinePlugin({
        APP_VERSION: `"${require('./package.json').version}"`,
        GIT_HASH: JSON.stringify(getGitHash()),
        BUILD_DATE: buildDate
      }),
      // 配置compression-webpack-plugin压缩
      new CompressionWebpackPlugin({
        algorithm: 'gzip',
        test: new RegExp('\\.(' + productionGzipExtensions.join('|') + ')$'),
        threshold: 10240,
        minRatio: 0.8
      })
    ],
    // if prod, add externals
    externals: {}
  },

  chainWebpack: config => {
    config.resolve.alias
      .set('@$', resolve('src'))
      .set('@assets', resolve('src/assets'))
      .set('@views', resolve('src/views'))

    //配置svg
    const svgRule = config.module.rule('svg')

    svgRule.exclude.add(resolve('src/assets/icons')).end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
  },

  css: {
    loaderOptions: {
      less: {
        modifyVars: {
          // less vars，customize ant design theme
          'primary-color': '#2f54eb'
          // 'link-color': '#F5222D',
          //'border-radius-base': '2px'
        },
        // DO NOT REMOVE THIS LINE
        javascriptEnabled: true
      }
    }
  },

  devServer: {
    // development server port 8000
    port: 8000,
    // host: '0.0.0.0',
    open: false, //启动服务时自动打开浏览器访问
    https: false,
    // 开启关闭https请求
    hotOnly: false,
    // 热更
    // If you want to turn on the proxy, please remove the mockjs /src/main.jsL11
    proxy: {
      // detail: https://cli.vuejs.org/config/#devserver-proxy
      // [process.env.VUE_APP_BASE_API]: {
      [process.env.VUE_APP_BASE_API]: {
        // target: `https://aidex.setworld.net`,
        // target: `http://*************:2222/`,
        // target: `http://**************:8810`,
        target: process.env.VUE_APP_PORT,
        changeOrigin: true, // 如果接口跨域，需要进行这个参数配置
        ws: false, // proxy websockets
        logLevel: 'debug',
        pathRewrite: {
          // ['^' + process.env.VUE_APP_BASE_API]: '',
          ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      }
    },
    disableHostCheck: true //增加该设置是为了解决使用外网映射工具映射时报错--可以删除
  },

  // disable source map in production
  productionSourceMap: false,
  lintOnSave: undefined,
  // babel-loader no-ignore node_modules/*
  transpileDependencies: []
}

// preview.pro.loacg.com only do not use in your production;
if (process.env.VUE_APP_PREVIEW === 'true') {
  console.log('VUE_APP_PREVIEW', true)
  // add `ThemeColorReplacer` plugin to webpack plugins
  vueConfig.configureWebpack.plugins.push(createThemeColorReplacerPlugin())
}

module.exports = vueConfig
