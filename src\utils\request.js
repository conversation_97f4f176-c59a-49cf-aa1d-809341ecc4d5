import axios from 'axios'
import store from '@/store'
import storage from 'store'
import notification from 'ant-design-vue/es/notification'
import { VueAxios } from './axios'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import errorCode from '@/utils/errorCode'
import Cookies from 'js-cookie'

// 创建 axios 实例
const request = axios.create({
  // API 请求的默认前缀
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: 20000, // 请求超时时间
})

// 异常拦截处理器，统一处理请求异常
const errorHandler = error => {
  let { message } = error
  if (message === 'Network Error') {
    message = '后端接口连接异常'
  } else if (message.includes('timeout')) {
    message = '系统接口请求超时'
  } else if (message.includes('Request failed with status code')) {
    message = '系统接口' + message.substr(message.length - 3) + '异常'
  } else if (message.includes('Request Parameter Error')) {
    message = '系统接口参数异常:' + message
  } else {
  }
  notification.error({
    message: message,
    duration: 5,
  })
  //  return Promise.reject(error) //注释该行，否则接口请求失败会一直请求刷新错误数据，这里保证请求一次
}

// request interceptor
// 请求拦截器
request.interceptors.request.use(config => {
  const token = storage.get(ACCESS_TOKEN)
  // 如果 token 存在
  // 让每个请求携带自定义 token 请根据实际情况自行修改
  if (token) {
    config.headers['token'] = token // 让每个请求携带自定义token 请根据实际情况自行修改
  }
  // get请求映射params参数
  if (config.method === 'get' && config.params) {
    let url = config.url + '?'
    for (const propName of Object.keys(config.params)) {
      const value = config.params[propName]
      var part = encodeURIComponent(propName) + '='
      // 修改漏洞
      if (value != null && typeof value !== 'undefined') {
        if (typeof value === 'object') {
          for (const key of Object.keys(value)) {
            const params = propName + '[' + key + ']'
            var subPart = encodeURIComponent(params) + '='
            url += subPart + encodeURIComponent(value[key]) + '&'
          }
        } else {
          url += part + encodeURIComponent(value) + '&'
        }
      }
    }
    url = url.slice(0, -1)
    config.params = {}
    config.url = url
  }
  // 统一设置请求头
  return config
}, errorHandler)

// response interceptor
// 响应拦截器
request.interceptors.response.use(res => {
  // 请求rul
  const requestUrl = res.config.url
  // 未设置状态码则默认成功状态
  const code = res.data.code || 200
  // 获取错误信息
  const msg = res.data.message

  if (code == 200 && res.data.success == false) {
    notification.error({
      message: '系统提示',
      description: code + ':' + msg,
      duration: 5,
    })
  } else if (code === 401) {
    notification.error({
      message: '系统提示',
      description: code + ':' + msg,
      duration: 5,
    })
    storage.remove(ACCESS_TOKEN)
    // Cookies.remove("serveErr")
    window.location.href = `${process.env.VUE_APP_CAS_URL}/login?service=${window.location.origin}${
      window.location.pathname === '/' ? '' : window.location.pathname
    }`
  } else if (code === 404) {
    notification.error({
      key: 'loginExpireTip',
      message: '系统提示',
      description: code + ':' + msg,
      duration: 5,
    })
  } else if (code == 421 || code == 403) {
    Cookies.set('serveErr', JSON.stringify(res.data))
  } else if (code === 500) {
    notification.error({
      key: 'loginExpireTip',
      message: '系统提示',
      description: code + ':' + msg,
      duration: 5,
    })
    Cookies.set('serveErr', JSON.stringify(res.data))
    // this.$router.push({ path: '/500' })
  } else if (code !== 200) {
    notification.error({
      message: '系统提示',
      description: code + ':' + msg,
      duration: 5,
    })
    Cookies.set('serveErr', JSON.stringify(res.data))
    // this.$router.push({ path: '/500' })
  } else {
    return res.data
  }
  return Promise.reject(msg)
}, errorHandler)

const installer = {
  vm: {},
  install(Vue) {
    Vue.use(VueAxios, request)
  },
}

export default request

export { installer as VueAxios, request as axios }
