import router from './router'
import store from './store'
import storage from 'store'
import NProgress from 'nprogress' // progress bar
import '@/components/NProgress/nprogress.less' // progress bar custom style
import notification from 'ant-design-vue/es/notification'
import { setDocumentTitle, domTitle } from '@/utils/domUtil'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import Cookies from 'js-cookie'
import QueryString from 'query-string'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const allowList = ['login', 'applyLicense', '404', '403', '500'] // no redirect allowList
const loginRoutePath = '/user/login'
const defaultRoutePath = '/index'

router.beforeEach((to, from, next) => {
  // incoming route
  NProgress.start() // start progress bar
  to.meta && typeof to.meta.title !== 'undefined' && setDocumentTitle(`${to.meta.title} - ${domTitle}`)

  // 存储params参数到本地
  const paramsJson = JSON.stringify(to.params)
  if (paramsJson !== '{}') {
    localStorage.setItem('routerParams' + to.name, paramsJson)
  }

  /* has token */
  if (storage.get(ACCESS_TOKEN)) {
    if (to.path === loginRoutePath || to.path === '/') {
      next({ path: defaultRoutePath })
      NProgress.done()
    } else {
      // console.log('store.getters',store.getters,store);
      // check login user.roles is null
      if (store.getters.roles.length === 0) {
        // request login userInfo
        store
          .dispatch('GetInfo')
          .then(res => {
            // 获取用户信息，根据用户角色分配角色
            storage.set('userId', res.data.userId)
            // res.roles = ['admin']
            const roles = res.roles
            // generate dynamic router
            store
              .dispatch('GenerateRoutes', { roles })
              .then(() => {
                // 根据roles权限生成可访问的路由表

                // 动态添加可访问路由表
                router.addRoutes(store.getters.addRouters)
                // 请求带有 redirect 重定向时，登录自动重定向到该地址
                next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
              })
              .catch(res => {})
          })
          .catch(res => {})
      } else {
        next()
      }
    }
  } else {
    if (allowList.includes(to.name)) {
      // 在免登录名单，直接进入
      next()
    } else {
      if (to.query?.ticket) {
        if (Array.isArray(to.query.ticket)) {
          sessionStorage.setItem('ticket', to.query.ticket[to.query.ticket.length - 1])
        } else {
          sessionStorage.setItem('ticket', to.query.ticket)
        }
        let quy = { ...to.query }
        Reflect.deleteProperty(quy, 'ticket')
        next({
          path: loginRoutePath,
          query: { redirect: `${to.path}${Object.keys(quy).length ? `?${QueryString.stringify(quy)}` : ''}` },
        })
      } else {
        next({ path: loginRoutePath, query: { redirect: to.fullPath } })
      }

      NProgress.done() // if current page is login will not trigger afterEach hook, so manually handle it
    }
  }
})

router.afterEach(() => {
  NProgress.done() // finish progress bar
})
