<template>
  <div :class="prefixCls">
    <ul>
      <li
        v-for="(icon, idx) in icons"
        :key="idx"
        :class="{ active: selectedIcon == icon }"
        @click="handleSelectedIcon(icon)"
      >
        <MenusIcon :iconClass="icon" class="svg-icon" :style="{ fontSize: '24px' }" />
        <div class="name">{{ icon }}</div>
      </li>
    </ul>
  </div>
</template>

<script>
  // import { menusNames as icons } from '@/assets/icons/index.js'
  import { MenusIcon, menusNames } from 'hhzk-vue2-components'

  export default {
    name: 'IconSelect',
    components: { MenusIcon },
    props: {
      prefixCls: {
        type: String,
        default: 'ant-pro-icon-selector'
      },
      // eslint-disable-next-line
      value: {
        type: String
      },
      svgIcons: {
        type: Array,
        required: true
      }
    },
    data() {
      return {
        selectedIcon: this.value || '',
        icons: menusNames
      }
    },
    watch: {
      value(val) {
        this.selectedIcon = val
      }
    },
    created() {},
    methods: {
      handleSelectedIcon(icon) {
        this.selectedIcon = icon
        this.$emit('change', icon)
      }
    }
  }
</script>

<style lang="less" scoped>
  @import '../index.less';

  ul {
    list-style: none;
    padding: 0;
    overflow-y: scroll;
    height: 250px;

    li {
      display: inline-block;
      padding: 10px 9px;
      border-radius: @border-radius-base;
      overflow: hidden;
      text-align: center;
      .name {
        width: 58px;
        font-size: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &:hover,
      &.active {
        // box-shadow: 0px 0px 5px 2px @primary-color;
        cursor: pointer;
        background-color: @primary-color;
        color: #fff;
      }
    }
  }
</style>
