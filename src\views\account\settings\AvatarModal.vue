<template>

  <a-modal
    title="修改头像"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    :width="800"
    :footer="null"
    @cancel="cancelHandel">
    <a-row>
      <a-col :xs="24" :md="12" :style="{height: '350px'}">
        <vue-cropper
          ref="cropper"
          :img="options.img"
          :info="true"
          :autoCrop="options.autoCrop"
          :autoCropWidth="options.autoCropWidth"
          :autoCropHeight="options.autoCropHeight"
          :fixed="options.fixed"
          :fixedNumber="options.fixedNumber"
          :full="options.full"
          :fixedBox="options.fixedBox"
          :outputType="options.outputType"
          @realTime="realTime"
        >
        </vue-cropper>
      </a-col>
      <a-col :xs="24" :md="12" :style="{height: '350px'}">
        <div class="avatar-upload-preview">
          <img :src="previews.url" :style="previews.img"/>
        </div>
      </a-col>
    </a-row>
    <br>
    <a-row>
      <a-col :lg="2" :md="2">
        <a-upload name="file" :beforeUpload="beforeUpload" :showUploadList="false">
          <a-button icon="upload">选择图片</a-button>
        </a-upload>
      </a-col>
      <a-col :lg="{span: 1, offset: 2}" :md="2">
        <a-button icon="plus" @click="changeScale(1)"/>
      </a-col>
      <a-col :lg="{span: 1, offset: 1}" :md="2">
        <a-button icon="minus" @click="changeScale(-1)"/>
      </a-col>
      <a-col :lg="{span: 1, offset: 1}" :md="2">
        <a-button icon="undo" @click="rotateLeft"/>
      </a-col>
      <a-col :lg="{span: 1, offset: 1}" :md="2">
        <a-button icon="redo" @click="rotateRight"/>
      </a-col>
      <a-col :lg="{span: 2, offset: 6}" :md="2">
        <a-button type="primary" @click="finish('blob')">保存</a-button>
      </a-col>
    </a-row>
  </a-modal>

</template>
<script>
import store from '@/store'
import { uploadAvatar } from '@/api/system/user'
import { mapGetters } from 'vuex'
  import uuid4 from '@/utils/guid.js'
  let Minio = require('minio')
  let stream = require('stream')

export default {
  data () {
    return {
      visible: false,
      id: null,
      confirmLoading: false,
      fileList: [],
      uploading: false,
      options: {
        // img: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
        img: '',
        autoCrop: true,
        autoCropWidth: 200,
        autoCropHeight: 200,
          fixedBox: true,
          outputType: 'png',
          fixed: true, //是否开启截图框宽高固定比例
          fixedNumber: [4, 4], //截图框的宽高比例
          full: true, //false按原比例裁切图片，不失真
          fixedBox: false, //固定截图框大小，不允许改变

          // centerBox: true,
          // canScale: false,
          // canMove: false,
          // canMoveBox: false,
          // canScale: false,
          // original: false,
          // canDrag: false,
          // info: true,
        },
        files: {},
        folderName: undefined,
        previews: {},
    }
  },
  computed: {
    ...mapGetters(['avatar'])
  },
    created() {
      const urlObj = new URL(process.env.VUE_APP_MINIO_URL)
      //连接minio文件服务器
      this.minioClient = new Minio.Client({
        endPoint: urlObj.hostname, //对象存储服务的URL
        port: +urlObj.port, //端口号
        useSSL: urlObj.protocol === 'https:', //true代表使用HTTPS
        accessKey: process.env.VUE_APP_MINIO_ACCESSKEY, //账户id
        secretKey: process.env.VUE_APP_MINIO_SECRETKEY, //密码
        partSize: '50M',
      })
    },
  methods: {
    edit (id) {
      this.visible = true
      this.id = id
        // this.options.img = this.avatar
      /* 获取原始头像 */
    },
    close () {
      this.id = null
      this.visible = false
    },
    cancelHandel () {
      this.close()
    },
    changeScale (num) {
      num = num || 1
      this.$refs.cropper.changeScale(num)
    },
    rotateLeft () {
      this.$refs.cropper.rotateLeft()
    },
    rotateRight () {
      this.$refs.cropper.rotateRight()
    },
    beforeUpload (file) {
      const reader = new FileReader()
      // 把Array Buffer转化为blob 如果是base64不需要
      // 转化为base64
      reader.readAsDataURL(file)
        this.files = file
      reader.onload = () => {
        this.options.img = reader.result
      }
      // 转化为blob
      // reader.readAsArrayBuffer(file)

      return false
    },

    // 上传图片（点击上传按钮）
    finish (type) {
      console.log('finish')
      const _this = this
      const formData = new FormData()
      // 输出
      if (type === 'blob') {
          this.$refs.cropper.getCropBlob(async data => {
            const img = window.URL.createObjectURL(data)
            this.model = true
            // this.modelSrc = img

            let formData = new FormData()
            formData.append('file', data, _this.files.name) //创建文件类型的FormData，将裁剪后的图片数据放入
            let tmpFile = formData.get('file')
            //获取文件类型及大小
            const fileName = tmpFile.name
            const mineType = tmpFile.type
            const fileSize = tmpFile.size
            //参数
            let metadata = {
              'content-type': mineType,
              'content-length': fileSize,
            }

            _this.fileToBuf(tmpFile, (buf, filename) => {
              //定义流
              let bufferStream = new stream.PassThrough()
              //将buffer写入
              bufferStream.end(new Buffer(buf))

              const bucketName = process.env.VUE_APP_MINIO_BUCKET
              const pathName = _this.folderName ? `${_this.folderName}/${fileName}` : fileName

              let arr = pathName.split('.')

              const pathNameAndUid = `${arr.slice(0, arr.length - 1).join('.')}-uuid-${uuid4()}.${arr[arr.length - 1]}`

              this.minioClient.putObject(
                bucketName,
                pathNameAndUid,
                bufferStream,
                fileSize,
                metadata,
                (error, etag) => {
                  if (error == null) {
                    const fileUrl = `${process.env.VUE_APP_MINIO_URL}/${bucketName}/${pathNameAndUid}`
                    //输出url
                    let only = { url: fileUrl, name: pathNameAndUid }
                    // onSuccess({ status: 200, statusCode: 200, url: only.url }, file)
                    _this.$message.success('上传成功')
                    // _this.$emit('ok', process.env.VUE_APP_BASE_API + response.imgUrl)
                    _this.$emit('ok', only.url)
                    _this.visible = false
                    console.log('cropper upload success', only)
                  }
                }
              )
            })

            // formData.append('avatarfile', data, this.fileName)
            // uploadAvatar(formData).then(response => {
            //   this.open = false
            //   console.log(process.env.VUE_APP_BASE_API + response.imgUrl)
            //   // this.options.img = process.env.VUE_APP_BASE_API + response.imgUrl
            //   store.commit('SET_AVATAR', process.env.VUE_APP_BASE_API + response.imgUrl)

            // })
          })
        } else {
          this.$refs.cropper.getCropData(data => {
            this.model = true
            // this.modelSrc = data
          })
        }
      },
      // File转arraybuffer
      fileToBuf(file, cb) {
        var fr = new FileReader()
        var filename = file.name

        fr.readAsArrayBuffer(file)
        fr.addEventListener(
          'loadend',
          e => {
            var buf = e.target.result
            cb(buf, filename)
          },
          false
        )
    },
    okHandel () {
      const vm = this

      vm.confirmLoading = true
      setTimeout(() => {
        vm.confirmLoading = false
        vm.close()
        vm.$message.success('上传头像成功')
      }, 2000)
    },

    realTime (data) {
      this.previews = data
    }
  }
}
</script>

<style lang="less" scoped>

  .avatar-upload-preview {
    position: absolute;
    top: 50%;
    transform: translate(50%, -50%);
    width: 180px;
    height: 180px;
    border-radius: 5px;
    // border-radius: 50%;
    box-shadow: 0 0 4px #ccc;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
    }
  }
</style>
