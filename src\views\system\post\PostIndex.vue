<template>
  <div>
    <div class="common-table-page">
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="关键字">
          <a-input
            v-model="queryParam.keywords"
            placeholder="请输入岗位名称/岗位编码"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <a-form-item label="岗位状态">
          <a-select placeholder="请选择岗位状态" v-model="queryParam.isDisabled" style="width: 100%" allow-clear>
            <a-select-option value="0">正常</a-select-option>
            <a-select-option value="1">停用</a-select-option>
          </a-select>
        </a-form-item>

        <template #table>
          <VxeTable
            ref="vxeTableRef"
            tableTitle="岗位管理"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            @refresh="getList"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @selectChange="selectChange"
            @handlePageChange="handlePageChange"
            :expand-config="{ accordion: true }"
          >
            <div class="table-operations" slot="button">
              <a-button type="primary" size="small" @click="handleAdd()">
                <a-icon type="plus" />
                新增
              </a-button>
              <a-button type="danger" v-if="isChecked" @click="handleDelete">
                <a-icon type="delete" />
                删除
              </a-button>
            </div>
          </VxeTable>
        </template>
      </VxeTableForm>

      <!-- 增加 -->
      <post-add-form
        v-if="showAddModal"
        ref="postAddForm"
        :statusOptions="statusOptions"
        @ok="getList"
        @close="showAddModal = false"
      />
      <!-- 修改 -->
      <post-edit-form
        v-if="showEditModal"
        ref="postEditForm"
        :statusOptions="statusOptions"
        @ok="getList"
        @close="showEditModal = false"
      />
    </div>
  </div>
</template>
<script>
  import { listPost, delPost, exportPost, listPagePost, dictPostStatus } from '@/api/system/post'
  import PostEditForm from './modules/PostEditForm'
  import PostAddForm from './modules/PostAddForm'
  // import AdvanceTable from '@/components/pt/table/AdvanceTable'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'

  const defaultQueryParamQuery = {
    pageNum: 1,
    isDisabled: undefined,
    keywords: '',
    pageSize: 10,
    sort: [],
  }
  export default {
    name: 'Post',
    components: {
      PostEditForm,
      PostAddForm,
      VxeTable,
      VxeTableForm,
    },
    data() {
      return {
        showAddModal: false,
        showEditModal: false,
        list: [],
        selectedRowKeys: [],
        selectedRows: [],
        // 高级搜索 展开/关闭
        advanced: false,
        // 非单个禁用
        single: true,
        // 非多个禁用
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        sunloading: false,
        total: 0,
        // 类型数据字典
        statusOptions: [],
        // 日期范围
        dateRange: [],
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        queryParam: Object.assign({}, defaultQueryParamQuery),
        addModalRefName: 'addModal', // 添加弹窗ref名称
        columns: [
          { type: 'checkbox', width: 30, align: 'center' },
          {
            title: '岗位编码',
            field: 'postCode',
            showOverflow: 'tooltip',
            align: 'center',
          },
          {
            title: '岗位名称',
            field: 'postName',
            showOverflow: 'tooltip',
            align: 'center',
          },
          {
            title: '排序',
            field: 'sort',
            align: 'center',
          },
          {
            title: '岗位状态',
            field: 'isDisabled',
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <div class='is-disabled'>
                    <a-switch
                      slot='actions'
                      size='small'
                      style={{
                        'background-color': row.isDisabled == 0 ? '#52c41a' : '#fc011a',
                      }}
                      checked={row.isDisabled == 0}
                      onChange={$event => this.onChangeStatus($event, row)}
                    />
                    <span
                      style={{
                        'font-size': '12px',
                        color: row.isDisabled == 0 ? '#52c41a' : '#fc011a',
                        'margin-left': '5px',
                      }}
                    >
                      {row.isDisabled == '0' ? '正常' : '停用'}
                    </span>
                  </div>
                )
              },
            },
          },
          {
            title: '创建时间',
            field: 'createdTime',
            showOverflow: 'tooltip',
            align: 'center',
          },
          {
            title: '备注',
            field: 'remark',
          },

          {
            title: '操作',
            field: 'operate',
            width: 100,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a-button type='link' onClick={() => this.handleUpdate(row, undefined)}>
                      修改
                    </a-button>
                    <a-divider type='vertical' />
                    <a-button type='link' onClick={() => this.handleDelete(row)}>
                      删除
                    </a-button>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    filters: {},
    created() {
      this.getList()
      /* this.getDicts('sys_normal_disable').then(response => {
      this.statusOptions = response.data
    }) */
    },
    computed: {},
    watch: {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = !pageSize ? 9 : pageSize
        this.getList()
      },
      /** 查询定时任务列表 */
      async getList() {
        this.showAddModal = false
        this.showEditModal = false
        this.loading = true
        this.selectChange({ records: [] })
        const { data: res } = await listPagePost(this.queryParam)
        this.list = res.data
        this.total = res.total
        this.loading = false
      },
      // 岗位状态字典翻译
      /* statusFormat (row) {
      return this.selectDictLabel(this.statusOptions, row.status)
    }, */
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      handleAdd(record) {
        this.showAddModal = true
        this.$nextTick(() => this.$refs.postAddForm.handleAdd(record))
      },
      handleUpdate(record, ids) {
        this.showEditModal = true
        this.$nextTick(() => this.$refs.postEditForm.handleUpdate(record, ids))
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = []
        this.queryParam = {
          pageNum: 1,
          isDisabled: '',
          keywords: '',
          pageSize: 10,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.postId)
        this.names = valObj.records.map(item => item.postName)
        this.isChecked = !!valObj.records.length
      },

      toggleAdvanced() {
        this.advanced = !this.advanced
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row.postId || this.ids
        const postNames = row.postName || this.names
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中岗位名称为"' + postNames + '"的数据',
          onOk() {
            return delPost(ids).then(res => {
              that.selectChange({ records: [] })
              that.getList()
              that.$message.success('删除成功', 3)
            })
          },
          onCancel() {},
        })
      },
      onChangeStatus(e, record) {
        var that = this
        const postId = record.postId
        const postName = record.postName

        let isDisabled = 0
        let recordIsDisabled = !record.isDisabled
        isDisabled = recordIsDisabled ? 1 : 0
        let isDisabledName = isDisabled == 1 ? '停用' : '启用'
        this.$confirm({
          title: '是否"' + isDisabledName + '"所选中数据?',
          content: '当前选中的数据:' + postName,
          onOk() {
            return dictPostStatus(isDisabled, postId).then(res => {
              if (res.code == 200 && res.success == true) {
                that.getList()
                that.$message.success('岗位状态"' + isDisabledName + '"成功！')
              } else {
                that.$message.success('"' + isDisabledName + '"失败:', res.message)
              }
            })
          },
          onCancel() {},
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  .is-disabled {
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
