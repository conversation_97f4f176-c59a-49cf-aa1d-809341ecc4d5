import request from '@/utils/request'

// 查询在线用户列表
// export function list(query) {
//   return request({
//     url: '/monitor/online/list',
//     method: 'get',
//     params: query
//   })
// }

// 查询在线用户列表
export function list(query) {
  return request({
    url: '/sys/onlineUser/list',
    method: 'post',
    data: query
  })
}

// 强退用户
// export function forceLogout(tokenId) {
//   return request({
//     url: '/monitor/online/' + tokenId,
//     method: 'delete'
//   })
// }

// 强退用户
export function forceLogout(token) {
  return request({
    url: '/sys/onlineUser/kickoff',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: 'tokens=' + token
  })
}