<template>
  <a-modal :title="title" :visible="open" @ok="submitForm" @cancel="cancel">
    <a-form-model ref="form" :model="form" :rules="rules">
      <a-form-model-item label="用户名" prop="username">
        <a-input v-model="form.username" :disabled="true" />
      </a-form-model-item>
      <a-form-model-item has-feedback label="新密码" prop="newPassword">
        <a-input-password
          v-model="form.newPassword"
          autocomplete=" "
          placeholder="请输入新密码"
          @change="checkPWD($event)"
        />
      </a-form-model-item>
      <PasswordCheck
        :newpwd="form.newPassword"
        :style="{ width: '470px', 'margin-bottom': '20px', 'margin-left': '5px' }"
      />
      <a-form-model-item has-feedback label="确认密码" prop="confirmPassword">
        <a-input-password v-model="form.confirmPassword" autocomplete=" " placeholder="请确认密码" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
  import { resetUserPwd } from '@/api/system/user'
  import { getConfigKey } from '@/api/system/config.js'
  import clickThrottle from '@/utils/clickThrottle'
  import md5 from 'md5'
  import { PasswordCheck } from '@/components'

  export default {
    name: 'ResetPassword',
    components: { PasswordCheck },
    props: {},
    data() {
      const validateNewPass = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入新密码'))
        } else if (value.length <= 0) {
          callback(new Error('不能为空'))
        } else {
          if (this.form.confirmPassword !== '') {
            this.$refs.form.validateField('confirmPassword')
          }
          callback()
        }
      }
      const validateConfirmPass = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请再次输入新密码确认'))
        } else if (value !== this.form.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      }
      return {
        title: '重置密码',
        open: false,
        childrenDrawer: false,
        formLayout: 'horizontal',
        msgText: '',
        newpwd: '',
        form: {
          username: undefined,
          newPassword: undefined,
          confirmPassword: undefined,
        },
        rules: {
          newPassword: [{ required: true, validator: validateNewPass, trigger: 'change' }],
          confirmPassword: [{ required: true, validator: validateConfirmPass, trigger: 'change' }],
        },

        saveRegex: null,
        errorTips: null,
      }
    },
    created() {
      getConfigKey('sys.userPassword.regex').then(res => {
        this.saveRegex = new RegExp(res.data)
      })
      getConfigKey('sys.userPassword.errorTips').then(res => {
        this.errorTips = res.data
      })
    },
    watch: {},
    methods: {
      checkPWD(value) {
        this.newpwd = this.form.newPassword
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        this.form = {
          userId: undefined,
          username: undefined,
          newPassword: undefined,
          confirmPassword: undefined,
        }
      },
      handleResetPwd(row) {
        this.form = {
          userId: row.userId,
          username: row.username,
        }
        this.open = true
      },
      /** 重置密码按钮操作 */
      submitForm: function () {
        if (!clickThrottle(5000)) return
        this.$refs.form.validate(valid => {
          if (valid) {
            if (!this.saveRegex.test(this.form.newPassword)) {
              this.$message.warning(this.errorTips, 3)
              return
            }

            let md5Pwd = md5(this.form.newPassword)
            resetUserPwd(this.form.userId, md5Pwd).then(response => {
              this.$message.success('重置成功', 3)
              this.open = false
              this.$emit('close')
            })
          } else {
            return false
          }
        })
      },
    },
  }
</script>

<style scoped>
  #inputValue {
    width: 240px;
    margin-left: 20px;
    padding-left: 10px;
    border-radius: 3px;
  }
  .input_span label {
    margin-right: 10px;
  }
  .input_span span {
    display: inline-block;
    width: 54px;
    height: 16px;
    background: #cccccc;
    line-height: 16px;
    margin-right: 2px;
    text-align: center;
    color: #ffffff;
  }

  #weak {
    width: 120px;
  }

  #mezzo {
    width: 120px;
  }

  #strong {
    width: 120px;
  }
  #font span:nth-child(1) {
    color: red;
    margin-left: 80px;
  }
  #font span:nth-child(2) {
    color: orange;
    margin: 0 60px;
  }
  #font span:nth-child(3) {
    color: #00d1b2;
  }
</style>
