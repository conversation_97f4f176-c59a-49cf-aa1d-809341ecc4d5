<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="关键字">
        <a-input
          style="width: 100%"
          v-model="queryParam.keywords"
          placeholder="请输入参数名称/参数编码"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>
      <a-form-item label="参数值">
        <a-input
          style="width: 100%"
          v-model="queryParam.configValue"
          placeholder="请输入参数值"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="参数配置"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @selectChange="selectChange"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" size="small" @click="handleAdd">
              <a-icon type="plus" />
              新增
            </a-button>

            <a-button type="primary" @click="importData">
              <a-icon type="import" />
              导入
            </a-button>
            <a-button v-if="isChecked" @click="exportData">
              <a-icon type="export" />
              导出
            </a-button>

            <a-button type="" @click="handleRefreshCache">
              <a-icon type="redo" />
              刷新缓存
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>
      </template>
    </VxeTableForm>
    <!-- 增加组件按需加载 -->
    <config-add-form
      v-if="showAddModal"
      ref="configAddForm"
      @ok="getList"
      @close="showAddModal = false"
    />
    <!-- 修改组件按需加载 -->
    <config-edit-form
      v-if="showEditModal"
      ref="configEditForm"
      @ok="getList"
      @close="showEditModal = false"
    />

    <ImportExportJsonModal
      v-if="showImportExportJsonModal"
      ref="importExportJsonModalRef"
      @sendImportRequest="sendImportRequest"
      @afterImport="getList"
      @close="showImportExportJsonModal = false"
    />
  </div>
</template>
<script>
  import { listConfig, delConfig, cleanCache, exportConfig, importConfig } from '@/api/system/config'
  import ConfigEditForm from './modules/ConfigEditForm'
  import ConfigAddForm from './modules/ConfigAddForm'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import ImportExportJsonModal from '@/views/util/ImportExportJsonModal'

  export default {
    name: 'Config',
    components: {
      ConfigEditForm,
      ConfigAddForm,
      VxeTable,
      VxeTableForm,
      ImportExportJsonModal,
    },
    data() {
      return {
        showImportExportJsonModal: false,
        showAddModal: false,
        showEditModal: false,
        list: [],
        selectedRowKeys: [],
        selectedRows: [],
        // 高级搜索 展开/关闭
        advanced: false,
        // 非单个禁用
        single: true,
        // 非多个禁用
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        sunloading: false,
        total: 0,
        // 日期范围
        dateRange: [],
        labelCol: { span: 10 },
        wrapperCol: { span: 14 },
        queryParam: {
          pageNum: 1,
          pageSize: 10,
          configType: undefined,
          configValue: undefined,
          keywords: undefined,
          sort: [],
        },
        addModalRefName: 'addModal', // 添加弹窗ref名称
        columns: [
          { type: 'checkbox', width: 30, align: 'center' },
          { type: 'seq', title: '序号', align: 'center', width: 50 },
          {
            title: '参数编码',
            field: 'configKey',
            showOverflow: 'tooltip',
          },
          {
            title: '参数名称',
            field: 'configName',
            showOverflow: 'tooltip',
          },
          {
            title: '参数值',
            field: 'configValue',
            showOverflow: 'tooltip',
          },
         
          {
            title: '备注',
            field: 'remark',
            showOverflow: 'tooltip',
          },
          {
            title: '创建时间',
            field: 'createdTime',
          },
          {
            title: '操作',
            field: 'operate',
            width: 100,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a-button type='link' onClick={() => this.handleUpdate(row)}>
                      修改
                    </a-button>
                    <a-divider type='vertical' />
                    <a-button type='link' onClick={() => this.handleDelete(row)}>
                      删除
                    </a-button>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    filters: {},
    created() {
      this.getList()
    },
    computed: {},
    watch: {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = !pageSize ? 9 : pageSize
        this.getList()
      },
      exportData() {
        const that = this
        that.showImportExportJsonModal = true
        that.$nextTick(() => {
          that.$refs.importExportJsonModalRef.showExport({
            action: 'export',
            title: `导出参数【${that.names}】`,
          })
          that.$refs.importExportJsonModalRef.setModalLoading(true)
          exportConfig(that.ids)
            .then(res => {
              that.$refs.importExportJsonModalRef.setContent(res.data)
              that.$refs.importExportJsonModalRef.setModalLoading(false)
            })
            .catch(err => {
              that.$refs.importExportJsonModalRef.setModalLoading(false)
            })
        })
      },
      importData() {
        this.showImportExportJsonModal = true
        this.$nextTick(() => {
          this.$refs.importExportJsonModalRef.showImport({
            action: 'import',
            title: `导入参数`,
            content: '',
          })
        })
      },
      sendImportRequest(param) {
        importConfig(param.content)
          .then(res => {
            this.$refs.importExportJsonModalRef.handleImportResult(res)
          })
          .catch(err => {
            this.$refs.importExportJsonModalRef.setLoading(false)
            if (err) {
              console.log(err)
            }
          })
      },

      /** 查询定时任务列表 */
      getList() {
        this.showAddModal = false
        this.showEditModal = false
        this.loading = true
        this.selectChange({ records: [] })
        listConfig(this.queryParam).then(response => {
          this.list = response.data.data
          this.list.map(item => {
            item.operation = item.remark
          })
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = []
        this.queryParam = {
          pageNum: 1,
          pageSize: 10,
          configType: undefined,
          configValue: undefined,
          keywords: undefined,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.configId)
        this.names = valObj.records.map(item => item.configName)
        this.isChecked = !!valObj.records.length
      },

      toggleAdvanced() {
        this.advanced = !this.advanced
      },
      /** 清理缓存按钮操作 */
      handleRefreshCache() {
        cleanCache().then(response => {
          this.$message.success('刷新成功', 3)
        })
      },
      handleAdd() {
        this.showAddModal = true
        this.$nextTick(() => this.$refs.configAddForm.handleAdd())
      },
      handleUpdate(record) {
        this.showEditModal = true
        this.$nextTick(() => this.$refs.configEditForm.handleUpdate(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const configIds = row.configId || this.ids
        const configNames = row.configName || this.names
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + configNames + '"的数据',
          onOk() {
            return delConfig(configIds).then(() => {
              that.selectChange({ records: [] })
              that.getList()
              that.$message.success('删除成功', 3)
            })
          },
          onCancel() {},
        })
      },
    },
  }
</script>
