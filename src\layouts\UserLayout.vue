<template>
  <div id="userLayout" :class="['user-layout-wrapper', isMobile && 'mobile']">
    <div class="container">
      <router-view />   
    </div>
  </div>
</template>

<script>
import { deviceMixin } from "@/store/device-mixin";

export default {
  name: "UserLayout",
  mixins: [deviceMixin],
  mounted() {
    document.body.classList.add("userLayout");
  },
  beforeDestroy() {
    document.body.classList.remove("userLayout");
  },
};
</script>

<style lang="less" scoped>
#userLayout.user-layout-wrapper {
  height: 100%;

  &.mobile {
    .container {
      .main {
        max-width: 368px;
        width: 98%;
      }
    }
  }

  .container {
    width: 100%;
    min-height: 100%;
    background: #ffffff;
    background-size: 100%;
    vertical-align: middle;
    display: flex;

    a {
      text-decoration: none;
    }
    
    .top {
      text-align: center;
      width: 355px;
      height: 500px;
      background: url(~@/assets/login-bg.png) no-repeat center top;
      float: left;

      .header {
        height: 44px;
        line-height: 44px;

        .badge {
          position: absolute;
          display: inline-block;
          line-height: 1;
          vertical-align: middle;
          margin-left: -12px;
          margin-top: -10px;
          opacity: 0.8;
        }

        .logo {
          //height: 60px;
          //width: 100%;
          height: 120px;
          width: 80%;
          vertical-align: top;
          margin-top: 110px;
          margin-bottom: 10px;
          border-style: none;
        }

        .title {
          font-size: 24px;
          color: #ffffff;
          font-family: Avenir, "Helvetica Neue", Arial, Helvetica, sans-serif;
          font-weight: 600;
          position: relative;
          top: 2px;
        }
      }
      .desc {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
        margin-top: 12px;
        margin-bottom: 40px;
      }
    }

    .main {
      width: 445px;
      height: 500px;
      margin: 0 auto;
      float: left;
      display: block;
      padding: 0 30px;
      position: relative;
    }
    .footer {
      position: absolute;
      width: 100%;
      bottom: 0;
      padding: 0 16px;
      margin: 48px 0 24px;
      text-align: center;

      .links {
        margin-bottom: 8px;
        font-size: 14px;
        a {
          color: rgba(0, 0, 0, 0.45);
          transition: all 0.3s;
          &:not(:last-child) {
            margin-right: 40px;
          }
        }
      }
      .copyright {
        color: rgba(0, 0, 0, 0.45);
        font-size: 14px;
      }
    }
  }
}
</style>
