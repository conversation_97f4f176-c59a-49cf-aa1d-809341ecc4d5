import { getDicts } from '@/api/system/dict/data.js'
import './index.less'

export default {
  name: 'PasswordCheck',
  props: {
    newpwd: {
      type: String,
    },
    styles: {},
  },
  data() {
    return {
      strengthRegex: null,
    }
  },

  created() {
    getDicts('passwordStrengthRegpex').then(res => {
      this.strengthRegex = (res.data || []).reverse().map(el => ({ ...el, regex: new RegExp(el.dictValue) }))
    })
  },

  watch: {
    newpwd(newname, oldname) {
      if (!document.getElementById('weak')?.style) return
      this.msgText = this.checkStrong(newname)
      if (this.msgText === 'level1') {
        document.getElementById('weak').style.background = 'red'
      } else {
        document.getElementById('weak').style.background = '#cccccc'
      }
      if (this.msgText === 'level2') {
        document.getElementById('mezzo').style.background = 'orange'
      } else {
        document.getElementById('mezzo').style.background = '#cccccc'
      }
      if (this.msgText === 'level3') {
        document.getElementById('strong').style.background = '#00D1B2'
      } else {
        document.getElementById('strong').style.background = '#cccccc'
      }
    },
  },

  methods: {
    checkStrong(sValue) {
      let obj = this.strengthRegex.find(el => el.regex.test(sValue))
      switch (obj?.dictKey) {
        case 'L1':
          return 'level1'
        case 'L2':
          return 'level2'
        case 'L3':
          return 'level3'
      }
    },
  },
  render() {
    const { styles } = this.$props
    return (
      <div class='input_span' style={styles}>
        <label>安全强度:</label>
        <span id='weak'>弱</span>
        <span id='mezzo'>中</span>
        <span id='strong'>强</span>
      </div>
    )
  },
}
