{"name": "vue-antd-pro", "version": "3.0.0", "scripts": {"start": "vue-cli-service serve", "dev": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint", "build:dev": "vue-cli-service build --mode development", "build:test": "vue-cli-service build --mode test", "build:prod": "vue-cli-service build --mode production", "lint:nofix": "vue-cli-service lint --no-fix"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.5.0", "ant-design-vue": "1.7.2", "axios": "^0.19.0", "core-js": "^3.1.2", "echarts": "^5.0.0", "enquire.js": "^2.1.6", "hhzk-vue2-components": "^0.1.15", "highlight.js": "^10.5.0", "js-cookie": "^3.0.5", "lodash.clonedeep": "^4.5.0", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "md5": "^2.2.1", "minio": "^7.0.33", "mockjs2": "1.0.8", "moment": "^2.24.0", "nprogress": "^0.2.0", "query-string": "^7.1.3", "serve": "^14.2.0", "sortablejs": "^1.10.2", "store": "^2.0.12", "stream": "^0.0.2", "v-viewer": "^1.5.1", "vditor": "^3.7.3", "vue": "^2.6.12", "vue-clipboard2": "^0.2.1", "vue-container-query": "^0.1.0", "vue-copy-to-clipboard": "^1.0.3", "vue-cropper": "0.4.9", "vue-grid-layout": "^2.3.12", "vue-json-editor": "^1.4.3", "vue-router": "^3.1.2", "vue-svg-component-runtime": "^1.0.1", "vue-upload-component": "^2.8.20", "vuex": "^3.1.1", "vxe-table": "^3.6.13", "xe-utils": "^3.5.7", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.1/xlsx-0.20.1.tgz", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@ant-design/colors": "^3.2.1", "@vue/cli-plugin-babel": "^4.0.4", "@vue/cli-plugin-router": "^4.0.4", "@vue/cli-plugin-unit-jest": "^4.0.4", "@vue/cli-plugin-vuex": "^4.0.4", "@vue/cli-service": "^4.0.4", "@vue/test-utils": "^1.0.0-beta.29", "babel-plugin-import": "^1.12.2", "babel-plugin-transform-remove-console": "^6.9.4", "compression-webpack-plugin": "^5.0.1", "git-revision-webpack-plugin": "^3.0.6", "less": "^3.0.4", "less-loader": "^5.0.0", "opencollective": "^1.0.3", "opencollective-postinstall": "^2.0.2", "svg-sprite-loader": "^6.0.11", "vue-svg-icon-loader": "^2.1.1", "vue-template-compiler": "^2.6.12", "webpack-theme-color-replacer": "^1.3.12"}}