import { getMenu, saveMenu, updateMenu, getNextSort } from '@/api/system/menu'
import icons from '@/utils/requireIcons'
import IconSelector from '@/components/IconSelector'
import AntModal from '@/components/pt/dialog/AntModal'
import clickThrottle from '@/utils/clickThrottle'
import Cookies from 'js-cookie'
import { getOptions } from '@/api/system/dict/data.js'

export default {
  name: 'CreateForm',
  props: {
    linkOptions: {
      type: Array,
      required: true,
    },
    menuOptions: {
      type: Array,
      required: true,
    },
    menuTypeOptions: {
      type: Array,
      required: true,
    },
  },
  components: {
    IconSelector,
    AntModal,
  },
  data() {
    return {
      SelectIcon: 'down',
      hiddenDisabled: false,
      iconVisible: false,
      iconList: icons,
      loading: false,
      formTitle: '',
      currentRow: undefined,
      oldParentId: '',
      spinning: false,
      delayTime: 200,
      menuTypeEnableValue: [],
      editStatus: 1, //2新增，3修改

      clientOptions: [],

      // 表单参数
      form: {
        menuId: undefined,
        parentId: 0,
        menuName: undefined,
        icon: ' ',
        type: '1',
        openWith: undefined,
        menuCode: undefined,
        sort: 0,
        isLink: '1',
        route: undefined,
        component: undefined,
        perms: undefined,
        isHidden: 0,
        client: undefined,
        remark: undefined,
      },
      open: false,
      rules: {
        menuName: [{ required: true, message: '菜单名称不能为空', trigger: 'blur' }],
        menuCode: [{ required: true, message: '菜单编码不能为空', trigger: 'blur' }],
        treeSort: [{ required: true, message: '菜单顺序不能为空', trigger: 'blur' }],
        path: [{ required: true, message: '路由地址不能为空', trigger: 'blur' }],
      },
    }
  },
  filters: {},
  created() {},
  computed: {},
  watch: {},
  methods: {
    // 上级菜单选择
    onMenuTreeChange(parentId, label, extra) {
      const type = extra.triggerNode.$options.propsData.dataRef.type
      getNextSort(parentId !== undefined ? parentId : '0').then(response => {
        this.form.sort = response.data
      })
      if (type !== undefined) {
        this.getMenuTypeEnableValue(type)
      } else {
        this.menuTypeEnableValue = this.menuTypeOptions.map(function (item) {
          return item.menuTypeValue
        })
      }
    },
    filterIcons() {
      this.iconList = icons
      if (this.name) {
        this.iconList = this.iconList.filter(item => item.includes(this.name))
      }
    },
    hideIconSelect() {
      this.iconList = icons
      this.iconVisible = false
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.iconVisible = false
      this.reset()
      this.$emit('close')
    },
    // 表单重置
    reset() {},
    // 菜单类别使能，根据上级菜单显示菜单类型
    getMenuTypeEnableValue(parentMenuType) {
      const id = this.form.menuId
      //定义枚举类：0根目录、1应用、2目录、3菜单、4按钮
      const menuTypeArr = Object.freeze({
        Root: 0,
        Applications: 1,
        Catalogue: 2,
        Menu: 3,
        Button: 4,
      })
      /** 校验MenuType是否合理
       * 父节点 = 根节点, 子节点= 应用
       * 父节点 = 应用, 子节点 = 目录|| 菜单
       * 父节点 = 目录，子节点= 目录||菜单
       * 父节点 = 菜单, 子节点= 菜单 || 按钮
       * 父节点 = 按钮, 无子节点
       * **/
      if (parentMenuType == menuTypeArr.Root) {
        this.menuTypeEnableValue = this.menuTypeOptions
          .filter(function (item) {
            return item.menuTypeValue == menuTypeArr.Applications
          })
          .map(function (item) {
            return item.menuTypeValue
          })
      } else if (parentMenuType == menuTypeArr.Applications) {
        this.menuTypeEnableValue = this.menuTypeOptions
          .filter(function (item) {
            return item.menuTypeValue == menuTypeArr.Catalogue || item.menuTypeValue == menuTypeArr.Menu
          })
          .map(function (item) {
            return item.menuTypeValue
          })
      } else if (parentMenuType == menuTypeArr.Catalogue) {
        this.menuTypeEnableValue = this.menuTypeOptions
          .filter(function (item) {
            return item.menuTypeValue == menuTypeArr.Catalogue || item.menuTypeValue == menuTypeArr.Menu
          })
          .map(function (item) {
            return item.menuTypeValue
          })
      } else if (parentMenuType == menuTypeArr.Menu) {
        this.menuTypeEnableValue = this.menuTypeOptions
          .filter(function (item) {
            return item.menuTypeValue == menuTypeArr.Menu || item.menuTypeValue == menuTypeArr.Button
          })
          .map(function (item) {
            return item.menuTypeValue
          })
      } else if (parentMenuType == menuTypeArr.Button) {
        this.menuTypeEnableValue = this.menuTypeOptions
          .filter(function (item) {
            return item.menuTypeValue == menuTypeArr.Button
          })
          .map(function (item) {
            return item.menuTypeValue
          })
      }

      // 嵌套子页面
      if (this.menuTypeEnableValue.includes('3') && this.menuTypeEnableValue.includes('4')) {
        this.form.isHidden = 1
        this.hiddenDisabled = true
      }

      if (id !== null && id !== '' && id !== 'undefined' && id !== undefined) {
        // 编辑页面
        if (parentMenuType === null) {
          this.menuTypeEnableValue = this.menuTypeOptions
            .filter(function (item) {
              return item.menuTypeValue === menuTypeArr.Catalogue
            })
            .map(function (item) {
              return item.menuTypeValue
            })
        }
        // 编辑页面当切换上级部门后需要判断当前部门类型是否在可选集合，如果在类型保持不变，如果不在需要重新赋值
        const type = this.form.type
        const selectMenuType = this.menuTypeEnableValue.filter(function (item) {
          return item === type
        })
        this.form.type = selectMenuType.length == 0 ? this.menuTypeEnableValue[0] : type
      } else {
        // 添加页面
        this.form.type = this.menuTypeEnableValue[0]
      }
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()

      getOptions('client').then(res => {
        this.clientOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        this.form.client = this.clientOptions[0].value
      })

      /** 获取最大编号 */
      getNextSort(row !== undefined ? row.menuId : '0').then(response => {
        this.form.sort = response.data
      })
      this.menuTypeEnableValue = this.menuTypeOptions.map(function (item) {
        return item.menuTypeValue
      })
      this.$emit('select-tree')
      this.oldParentId = ''
      if (row != null && row.menuId) {
        this.currentRow = row
        this.oldParentId = row.menuId
        this.form.parentId = row.menuId
        this.getMenuTypeEnableValue(row.type)
      } else {
        this.form.parentId = 0
        this.getMenuTypeEnableValue(0)
      }
      this.open = true
      this.formTitle = '添加菜单'
      this.editStatus = 2
    },
    // 设置节点数据
    setNodeData(data) {
      this.currentRow.menuName = data.menuName
      this.currentRow.menuCode = data.menuCode
      this.currentRow.icon = data.icon ? data.icon : ' '
      this.currentRow.sort = data.sort
      this.currentRow.type = data.type
      this.currentRow.perms = data.perms
      this.currentRow.component = data.component
      this.currentRow.isLink = String(data.isLink)
      this.currentRow.parentId = data.parentId
      this.currentRow.route = data.route
      this.currentRow.remark = data.remark
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.spinning = !this.spinning
      this.open = true
      this.formTitle = '修改菜单'
      this.editStatus = 3
      this.currentRow = row
      this.reset()
      this.$emit('select-tree')

      getOptions('client').then(res => {
        this.clientOptions = res.data.map(el => ({ label: el.value, value: el.key }))
      })

      getMenu(row.menuId).then(response => {
        this.oldParentId = response.data.parentId
        this.form = response.data
        this.form.type = String(response.data.type)
        this.form.isLink = String(response.data.isLink)
        this.form.openWith = String(response.data.openWith)
        this.form.client = response.data.client
        this.spinning = !this.spinning
        this.menuTypeEnableValue = [response.data.type]

        // 父节点是菜单
        if (this.flatTreeMap[row.parentId].component && this.flatTreeMap[row.parentId].type === 3) {
          this.form.isHidden = 1
          this.hiddenDisabled = true
        }
      })
    },
    /** 提交按钮 */
    submitForm: function () {
      if (!clickThrottle(5000)) return
      this.$refs.form.validate(valid => {
        if (valid) {
          this.spinning = !this.spinning
          if (this.editStatus == 3) {
            updateMenu(this.form)
              .then(response => {
                if (response.code == 200 && response.success == true) {
                  this.$message.success('修改成功', 3)

                  if (this.oldParentId !== this.form.parentId) {
                    // 如果修改父节点则刷新树
                    this.$emit('ok')
                  } else {
                    // 设置节点数据
                    this.$emit('ok')
                  }
                  this.cancel()
                } else {
                  this.$message.success('修改失败:', response.message)
                  this.cancel()
                }
              })
              .catch(res => {
                console.log('修改失败:', res)
              })
          }
          if (this.editStatus == 2) {
            Cookies.set('isExpandId', this.form.parentId)
            saveMenu(this.form).then(response => {
              if (response.code == 200 && response.success == true) {
                this.$message.success('新增成功', 3)
                // 修改父节点后刷新整个树，如果直接添加子节点不更换父节点则追加节点
                if (this.oldParentId !== this.form.parentId) {
                  // 如果修改父节点则刷新树
                  this.$emit('ok')
                } else {
                  this.$emit('ok')
                }
                this.cancel()
              } else {
                this.$message.success('新增失败:', response.message)
                this.cancel()
              }
            })
          }
        } else {
          return false
        }
      })
    },
    // 图标更改
    handleIconChange(icon) {
      this.SelectIcon = 'down'
      this.iconVisible = false
      this.form.icon = icon
    },
    // 图标选择
    changeIcon(type) {
      this.currentSelectedIcon = type
    },
    // 图标查找
    selectIcon() {
      this.iconVisible = !this.iconVisible
      if (this.iconVisible) {
        this.SelectIcon = 'up'
      } else {
        this.SelectIcon = 'down'
      }
    },
    // 图标查找取消
    cancelSelectIcon() {
      this.iconVisible = false
    },
  },
}
