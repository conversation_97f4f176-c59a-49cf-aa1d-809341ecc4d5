<template>
  <div class="page-header-index-wide page-header-wrapper-grid-content-main padding-card">
    <a-row :gutter="24">
      <a-col :md="24" :lg="7">
        <a-spin :spinning="spinning" :delay="delayTime" tip="玩命加载中...">
          <a-card :bordered="false">
            <div class="account-center-avatarHolder">
              <div class="avatar">
                <img :src="avatar" v-if="user.avatar" />

                <a v-if="!user.avatar">
                  <a-avatar style="color: #fff; text-align: center; padding: 32% 0; background-color: #ff8801">
                    {{ showName }}
                  </a-avatar>
                </a>
              </div>

              <div class="username">{{ user.username }}</div>
              <!-- <div class="bio">{{ roleGroup }}</div> -->
            </div>
            <div class="account-center-detail">
              <p>
                <a-icon class="info" type="phone" />
                {{ user.mobile }}
              </p>
              <p>
                <a-icon class="info" type="mail" />
                {{ user.email }}
              </p>
              <p v-if="user.sysDept">
                <a-icon class="info" type="apartment" />
                {{ user.deptId }}
              </p>
              <p>
                <a-icon class="info" type="calendar" />
                {{ user.createdTime }}
              </p>
            </div>
            <a-divider />

            <div class="account-center-tags">
              <div class="tagsTitle">角色</div>
              <div>
                <span v-for="(tag, index) in roleGroupList" :key="index">
                  <a-tooltip :key="tag.roleId" :title="tag.roleName">
                    <a-tag>
                      {{ tag.roleName.length > 20 ? `${tag.roleName.slice(0, 20)}...` : tag.roleName }}
                    </a-tag>
                  </a-tooltip>
                </span>
              </div>
            </div>
            <div class="account-center-tags">
              <div class="tagsTitle">岗位</div>
              <div>
                <span v-for="(tag, index) in postGroupList" :key="index">
                  <a-tooltip :key="tag.postId" :title="tag.postName">
                    <a-tag>
                      {{ tag.postName.length > 20 ? `${tag.postName.slice(0, 20)}...` : tag.postName }}
                    </a-tag>
                  </a-tooltip>
                </span>
              </div>
            </div>
          </a-card>
        </a-spin>
      </a-col>
      <a-col :md="24" :lg="17">
        <a-card
          style="width: 100%"
          :bordered="false"
          :tabList="tabListNoTitle"
          :activeTabKey="noTitleKey"
          @tabChange="key => handleTabChange(key, 'noTitleKey')"
        >
          <base-setting ref="baseInfoRef" v-show="noTitleKey === 'baseInfo'"></base-setting>
          <updata-pass v-show="noTitleKey === 'updatePass'"></updata-pass>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
  import { PageView, RouteView } from '@/layouts'
  import BaseSetting from './BaseSetting'
  import UpdataPass from './UpdataPass'
  import { getUser } from '@/api/system/user'
  import { listRole } from '@/api/system/role'
  import { listPost } from '@/api/system/post'
  import { mapGetters } from 'vuex'
  import Cookies from 'js-cookie'
  import storage from 'store'
  export default {
    components: {
      RouteView,
      PageView,
      BaseSetting,
      UpdataPass
    },
    data() {
      return {
        user: {},
        roleGroup: {},
        postGroup: {},
        roleGroupList: [],
        postGroupList: [],
        roleOptions: [],
        postOptions: [],
        spinning: false,
        delayTime: 100,
        tags: ['很有想法的', '专注设计', '辣~', '大长腿', '川妹子', '海纳百川'],
        tagInputVisible: false,
        tagInputValue: '',
        showName: '',
        teams: [],
        teamSpinning: true,

        tabListNoTitle: [
          {
            key: 'baseInfo',
            tab: '基本信息'
          },
          {
            key: 'updatePass',
            tab: '修改密码'
          }
        ],
        noTitleKey: 'baseInfo'
      }
    },
    watch: {},
    computed: {
      ...mapGetters(['name', 'avatar'])
    },
    created() {
      this.getUser()
    },
    mounted() {},
    methods: {
      async getUser() {
        this.spinning = true
        await listRole().then(response => {
          this.roleOptions = response.data
        })
        await listPost().then(response => {
          this.postOptions = response.data
        })

        let userId = storage.get('userId')
        getUser(userId).then(response => {
          this.spinning = false
          const dataValue = response.data
          this.$refs.baseInfoRef.setUserInfo(dataValue)
          this.user = response.data
          this.roleGroup = dataValue.roleIds
          this.postGroup = dataValue.postIds
          this.roleGroupList = []
          this.postGroupList = []
          this.getPostGroupList(this.user.postIds)
          this.getRoleGroupList(this.user.roleIds)
          this.getShowName(this.user.name)
        })
      },

      getShowName(name) {
        if (name.length > 2) {
          name = name.substring(name.length - 2)
        }
        this.showName = name
        return name
      },
      getPostGroupList(postIds) {
        if (postIds) {
          if (!postIds.indexOf(',')) {
            this.postGroupList = this.postOptions.filter(item => item.postId == postIds)
          } else if (postIds.indexOf(',')) {
            postIds.forEach(item => {
              this.postGroupList.push(this.postOptions.filter(res => res.postId == item)[0])
            })
          }
        }
      },
      getRoleGroupList(roleIds) {
        if (roleIds) {
          if (!roleIds.indexOf(',')) {
            this.roleGroupList = this.roleOptions.filter(item => item.roleId == roleIds)
          } else if (roleIds.indexOf(',')) {
            roleIds.forEach(item => {
              this.roleGroupList.push(this.roleOptions.filter(res => res.roleId == item)[0])
            })
          }
        }
      },
      handleTabChange(key, type) {
        this[type] = key
      },

      handleTagClose(removeTag) {
        const tags = this.tags.filter(tag => tag !== removeTag)
        this.tags = tags
      },

      showTagInput() {
        this.tagInputVisible = true
        this.$nextTick(() => {
          this.$refs.tagInput.focus()
        })
      },

      handleInputChange(e) {
        this.tagInputValue = e.target.value
      },

      handleTagInputConfirm() {
        const inputValue = this.tagInputValue
        let tags = this.tags
        if (inputValue && !tags.includes(inputValue)) {
          tags = [...tags, inputValue]
        }

        Object.assign(this, {
          tags,
          tagInputVisible: false,
          tagInputValue: ''
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .page-header-wrapper-grid-content-main {
    width: 100%;
    height: 100%;
    min-height: 100%;
    transition: 0.3s;

    .account-center-avatarHolder {
      text-align: center;
      margin-bottom: 24px;

      & > .avatar {
        margin: 0 auto;
        width: 104px;
        height: 104px;
        margin-bottom: 20px;
        // border-radius: 50%;
        overflow: hidden;
        img {
          height: 100%;
          width: 100%;
        }
      }

      .ant-divider-horizontal {
        margin: 0;
        background: rgb(235, 237, 240);
      }

      .demo-split {
        height: 200px;
      }

      .demo-split-pane {
        padding: 10px;
      }

      .ant-avatar {
        width: 100%;
        height: 100%;
        font-size: 24px;
        border-radius: 4px;
        vertical-align: middle;
        margin-right: 8px;
      }

      .username {
        color: rgba(0, 0, 0, 0.85);
        font-size: 20px;
        line-height: 28px;
        font-weight: 500;
        margin-bottom: 4px;
      }
    }

    .account-center-detail {
      p {
        margin-bottom: 8px;
        padding-left: 26px;
        position: relative;
      }
      .info {
        position: absolute;
        height: 14px;
        width: 14px;
        left: 0;
        top: 4px;
      }
      .title {
        background-position: 0 0;
      }
      .group {
        background-position: 0 -22px;
      }
      .address {
        background-position: 0 -44px;
      }
    }

    .account-center-tags {
      .ant-tag {
        margin-bottom: 8px;
      }
    }

    .account-center-team {
      .members {
        a {
          display: block;
          margin: 12px 0;
          line-height: 24px;
          height: 24px;
          .member {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 24px;
            max-width: 100px;
            vertical-align: top;
            margin-left: 12px;
            transition: all 0.3s;
            display: inline-block;
          }
          &:hover {
            span {
              color: #1890ff;
            }
          }
        }
      }
    }

    .tagsTitle,
    .teamTitle {
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 12px;
    }
  }
</style>
