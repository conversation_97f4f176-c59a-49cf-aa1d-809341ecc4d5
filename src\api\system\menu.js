import request from '@/utils/request'

// 查询应用列表(无条件)
export function listMenu() {
  return request({
    url: '/sys/menu/menu/app/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}
// 获取父节点下的菜单
export function getMenuOfParent(parentId) {
  let data = 'parentId=' + parentId
  return request({
    url: '/sys/menu/children/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}
// 获取目录树
export function menuTree(menuId) {
  let data = menuId ? 'menuId=' + menuId : ''
  return request({
    url: '/sys/menu/parent/tree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}
// 查询菜单详情
export function getMenu(menuId) {
  let data = menuId ? 'menuId=' + menuId : ''
  return request({
    url: '/sys/menu/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}
// 获取排序号findMaxSort
export function getNextSort(parentId) {
  let data = 'parentId=' + parentId
  return request({
    url: '/sys/menu/getNextSort',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}
// 菜单树查询
export function searchMenuList(searchInfo) {
  return request({
    url: '/sys/menu/tree',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: searchInfo
  })
}
// 新增菜单
export function saveMenu(data) {
  return request({
    url: '/sys/menu/add',
    method: 'post',
    data: data
  })
}
// 编辑菜单
export function updateMenu(data) {
  return request({
    url: '/sys/menu/update',
    method: 'post',
    data: data
  })
}

// 删除菜单
export function delMenu(params) {
  return request({
    url: '/sys/menu/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 菜单状态:启用-停用
export function menuStatus(isDisabled, menuId) {
  let data = 'isDisabled=' + isDisabled
  data += menuId ? '&menuId=' + menuId : ''
  return request({
    url: '/sys/menu/switch',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}

// 查询应用列表(无条件)
export function getMenuList() {
  return request({
    url: '/sys/menu/app/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 复制菜单
export function copyMenu(params) {
  return request({
    url: '/sys/menu/copy',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

export function exportMenu(menuId) {
  return request({
    url: '/sys/menu/export',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: 'menuId=' + menuId
  })
}

export function importMenu(data) {
  return request({
    url: '/sys/menu/import',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  })
}
