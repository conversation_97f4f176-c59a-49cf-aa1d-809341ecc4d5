<template>
  <a-drawer
    width="480"
    :title="formTitle"
    :label-col="4"
    :wrapper-col="14"
    :visible="open"
    :body-style="{ height: 'calc(100vh - 100px)', overflow: 'auto' }"
    @close="cancel"
  >
    <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
      <a-spin :spinning="spinning" :delay="delayTime" tip="Loading...">
        <a-form-model-item label="上级菜单" prop="parentId">
          <a-tree-select
            v-model="form.parentId"
            style="width: 100%"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            :tree-data="menuOptions"
            placeholder="请选择"
            :replaceFields="{
              children: 'children',
              title: 'menuName',
              key: 'id',
              value: 'id',
            }"
            tree-default-expand-all
            @change="onMenuTreeChange"
          ></a-tree-select>
        </a-form-model-item>
        <a-form-model-item label="菜单类型" prop="type">
          <a-radio-group v-model="form.type" button-style="solid">
            <a-radio-button
              v-for="(d, index) in menuTypeOptions"
              :key="index"
              :disabled="menuTypeEnableValue.indexOf(d.menuTypeValue) === -1"
              :value="d.menuTypeValue"
            >
              {{ d.menuTypeLabel }}
            </a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="图标" v-if="form.type != 1" prop="icon">
          <div>
            <a-space size="large" class="selectIconBox">
              <span class="selectIcon">
                <SvgIcon :iconClass="form.icon" style="font-size: 16px" />
                {{ form.icon }}
              </span>
              <a @click="selectIcon" class="selectup">
                <a-icon :type="SelectIcon" />
              </a>
            </a-space>
          </div>
          <a-card :bordered="false" v-if="iconVisible">
            <icon-selector v-model="form.icon" @change="handleIconChange" :svgIcons="iconList" />
          </a-card>
        </a-form-model-item>
        <a-form-model-item label="图标" v-else prop="icon">
          <a-input v-model="form.icon" placeholder="请输入" />
        </a-form-model-item>
        <a-form-model-item label="菜单编码" prop="menuCode">
          <a-input v-model="form.menuCode" placeholder="请输入" />
        </a-form-model-item>
        <a-form-model-item label="菜单名称" prop="menuName">
          <a-input v-model="form.menuName" placeholder="请输入" />
        </a-form-model-item>
        <a-form-model-item label="标题" prop="title" v-if="form.type == '1'">
          <a-input v-model="form.title" placeholder="请输入" />
        </a-form-model-item>

        <a-form-model-item label="是否外链" prop="isLink" v-if="form.type != '4'">
          <a-select placeholder="请选择" v-model="form.isLink">
            <a-select-option v-for="(d, index) in linkOptions" :key="index" :value="d.dictKey">
              {{ d.dictValue }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="打开方式" prop="openWith" v-if="form.type == '3'">
          <a-radio-group v-model="form.openWith" button-style="solid">
            <a-radio-button value="1">新开页</a-radio-button>
            <a-radio-button value="2">当前页</a-radio-button>
            <a-radio-button value="3">应用内页</a-radio-button>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="路由地址" prop="route" v-if="form.type != '4'">
          <a-input v-model="form.route" placeholder="请输入" />
        </a-form-model-item>
        <a-form-model-item label="组件路径" prop="component" v-if="form.type == '3'">
          <a-input v-model="form.component" placeholder="请输入" />
        </a-form-model-item>
        <a-form-model-item label="权限标识" prop="perms" v-if="form.type != '2'">
          <a-input v-model="form.perms" placeholder="请输入" />
        </a-form-model-item>

        <a-form-model-item label="是否隐藏" prop="isHidden">
          <a-radio-group :disabled="hiddenDisabled" v-model="form.isHidden">
            <a-radio :value="0">显示</a-radio>
            <a-radio :value="1">隐藏</a-radio>
          </a-radio-group>
        </a-form-model-item>

        <a-form-model-item label="客户端" prop="client" v-if="form.type == '1'">
          <a-radio-group v-model="form.client" :options="clientOptions"></a-radio-group>
        </a-form-model-item>

        <a-form-model-item label="排序" prop="sort">
          <a-input-number v-model="form.sort" :min="0" style="width: 100%" />
        </a-form-model-item>

        <a-form-model-item label="备注" prop="remark">
          <a-input v-model="form.remark" placeholder="请输入" type="textarea" allow-clear />
        </a-form-model-item>
      </a-spin>
      <div class="bottom-control">
        <a-space>
          <a-button type="primary" @click="submitForm">保存</a-button>
          <a-button @click="cancel">取消</a-button>
        </a-space>
      </div>
    </a-form-model>
  </a-drawer>
</template>

<script>
  import MenuForm from './MenuForm'
  export default {
    ...MenuForm,
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');

  .selectIconBox {
    border: 1px solid @border-color-base;
    border-radius: @border-radius-base;
    height: @input-height-base;
    width: 100%;
    .anticon {
      color: @primary-color;
      font-size: 16px;
      padding: 0 12px;
    }
    .selectup {
      position: absolute;
      right: 0;
      top: 4px;
      width: 80%;
      text-align: right;
      .anticon {
        color: @text-color;
        font-size: @font-size-base;
      }
    }
    .ant-space-item {
      margin: 0 !important;
    }

    .selectIcon {
      margin-left: 10px;
      color: @text-color;
      font-size: 12px;
    }
  }
</style>
