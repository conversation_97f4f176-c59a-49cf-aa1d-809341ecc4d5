import request from '@/utils/request'

// 查询缓存详细
// export function getCache() {
//   return request({
//     url: '/monitor/cache',
//     method: 'get'
//   })
// }

// 查询缓存详细
export function getCache() {
  return request({
    url: '/sys/monitor/cache/dashboard',
    method: 'post'
  })
}

// 查询缓存名称列表
// export function listCacheName () {
//   return request({
//     url: '/monitor/cache/listCacheName',
//     method: 'get'
//   })
// }

// 查询缓存名称列表
export function listCacheName() {
  return request({
    url: '/sys/monitor/cache/list',
    method: 'post'
  })
}

// 删除缓存
// export function clearCache(cacheId) {
//   return request({
//     url: '/monitor/cache/clearCache/' + cacheId,
//     method: 'delete'
//   })
// }

// 删除缓存
export function clearCache(cacheKeyPrefix) {
  return request({
    url: '/sys/monitor/cache/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: 'cacheKeyPrefix=' + cacheKeyPrefix
  })
}

// 查询缓存详细
// export function listCacheKey(cacheId) {
//   return request({
//     url: '/monitor/cache/listCacheKey/' + cacheId,
//     method: 'get'
//   })
// }

// 查询缓存详细
export function listCacheKey(cacheKeyPrefix) {
  return request({
    url: '/sys/monitor/cache/key/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: 'cacheKeyPrefix=' + cacheKeyPrefix
  })
}

// 删除角色
// export function clearCacheByKey(cacheId, cacheKey) {
//   return request({
//     url: '/monitor/cache/clearCacheByKey/' + cacheId + '/' + cacheKey,
//     method: 'delete'
//   })
// }

// 删除键名
export function clearCacheByKey(cacheKey) {
  return request({
    url: '/sys/monitor/cache/key/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: 'cacheKey=' + cacheKey
  })
}

// 查询缓存详细
// export function getCacheValue(cacheId, cacheKey) {
//   return request({
//     url: '/monitor/cache/getCacheValue/' + cacheId + '/' + cacheKey,
//     method: 'get'
//   })
// }

// 查询缓存详细
export function getCacheValue(cacheId, cacheKey) {
  return request({
    url: '/sys/monitor/cache/key/value',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: 'cacheKey=' + cacheKey
  })
}