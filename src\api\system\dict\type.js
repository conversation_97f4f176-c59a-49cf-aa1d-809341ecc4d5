import request from '@/utils/request'

// 查询字典类型列表
export function listType(query) {
  return request({
    url: '/sys/dict/page',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: query
  })
}

// 查询字典类型详细
export function getType(dictId) {
  let data = dictId ? 'dictId=' + dictId : ''
  return request({
    url: '/sys/dict/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}

// 新增字典类型
export function saveType(data) {
  return request({
    url: '/sys/dict/add',
    method: 'post',
    data: data
  })
}
// 编辑字典类型
export function updateType(data) {
  return request({
    url: '/sys/dict/update',
    method: 'post',
    data: data
  })
}

// 删除字典类型
export function delType(dictIds) {
  let data = dictIds ? 'dictIds=' + dictIds : ''
  return request({
    url: '/sys/dict/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}

// 字典类型状态:启用-停用
export function dictStatus(isDisabled, dictId) {
  let data = 'isDisabled=' + isDisabled
  data += dictId ? '&dictId=' + dictId : ''
  return request({
    url: '/sys/dict/switch',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}

// 刷新字典缓存
export function cleanCache() {
  return request({
    url: '/sys/dict/cleanCache',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 导出字典类型
export function exportType(query) {
  return request({
    url: '/system/dict/type/export',
    method: 'get',
    params: query
  })
}

// 获取字典选择框列表
export function optionselect() {
  return request({
    url: '/system/dict/type/optionselect',
    method: 'get'
  })
}

// 查询字典类型列表
export function checkDictTypeUnique(data) {
  return request({
    url: 'system/dict/type/checkDictTypeUnique',
    method: 'get',
    params: data
  })
}

// 导出字典
export function exportDict(dictIds) {
  return request({
    url: '/sys/dict/export',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: 'dictIds=' + dictIds
  })
}

// 导入字典
export function importDict(data) {
  return request({
    url: '/sys/dict/import',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  })
}
