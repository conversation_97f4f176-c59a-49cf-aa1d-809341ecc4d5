import { getUser, addUser, updateUser, checkUserNameUnique, checkPhoneUnique } from '@/api/system/user'
import { listDeptTree } from '@/api/system/dept'
import AntModal from '@/components/pt/dialog/AntModal'
import clickThrottle from '@/utils/clickThrottle'
export default {
  name: 'CreateForm',
  props: {
    deptCheckedValue: {
      type: Number,
    },
    statusOptions: {
      type: Array,
      required: true,
    },
    deptOptions: {
      type: Array,
      required: true,
    },
    roleOptions: {
      type: Array,
      required: true,
    },
    postOptions: {
      type: Array,
      required: true,
    },
    sexOptions: {
      type: Array,
      required: true,
    },
    educationOptions: {
      type: Array,
      required: true,
    },
    workExperienceOptions: {
      type: Array,
      required: true,
    },
    defalutExpandedKeys: {
      type: Array,
    },
  },
  components: {
    AntModal,
  },
  data() {
    return {
      expandedKeys: this.defalutExpandedKeys,
      spinning: false,
      delayTime: 100,
      replaceFields: { children: 'children', title: 'label', key: 'id', value: 'id' },
      customStyle: 'background: #fff;ssborder-radius: 4px;margin-bottom: 24px;border: 0;overflow: hidden',
      // 默认密码
      initPassword: undefined,
      formTitle: '',
      // 表单参数
      form: {
        userId: undefined,
        avatar: undefined,
        birthday: undefined,
        deptId: undefined,
        email: undefined,
        idNo: undefined,
        mobile: undefined,
        name: undefined,
        password: undefined,
        postIds: [],
        remark: undefined,
        roleIds: [],
        sex: undefined,
        education: undefined,
        workExperience: undefined,
        title: undefined,
        username: undefined,
      },
      open: false,

      rules: {
        name: [{ required: true, message: '用户名称不能为空', trigger: 'blur' }],
        username: [
          { required: true, message: '用户名不能为空', trigger: 'blur' },
          { validator: this.checkUserNameUnique, trigger: 'change' },
        ],
        deptId: [{ required: true, message: '部门不能为空', trigger: 'change' }],
        mobile: [{ validator: this.checkPhoneUnique }],
      },
    }
  },
  filters: {},
  created() {
    this.initPassword = this.GLOBAL.INITPWD
    this.form.deptId = !this.form.userId ? this.deptCheckedValue : this.form.deptId
  },
  computed: {},
  watch: {},
  methods: {
    // 取消按钮
    cancel() {
      this.open = false
      this.$emit('close')
    },
    // 表单重置
    reset() {
      if (this.$refs.form !== undefined) {
        this.$refs.form.resetFields()
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$emit('select-tree')
      this.open = true
      this.formTitle = '新增用户'
      this.form.password = this.initPassword
    },
    /** 修改按钮操作 */
    handleUpdate(row, ids) {
      this.open = true
      this.formTitle = '修改【' + row.name + '】信息'
      this.spinning = !this.spinning
      this.$emit('select-tree')
      const id = row ? row.userId : ids
      getUser(id).then(response => {
        this.form = response.data
        this.form.sex = response.data.sex || response.data.sex==0 ? String(response.data.sex) : null
        this.form.birthday = response.data.birthday
        this.spinning = !this.spinning
      })
    },
    /** 提交按钮 */
    submitForm: function () {
      if (!clickThrottle(5000)) return
      this.$refs.form.validate(valid => {
        if (valid) {
          this.form.sex = this.form.sex|| this.form.sex == 0  ? String(this.form.sex) : null
          
          const saveForm = JSON.parse(JSON.stringify(this.form))
        
          if (this.form.userId !== undefined) {
            updateUser(saveForm).then(response => {
              this.$message.success('修改成功', 3)
              this.open = false
              this.$emit('ok')
            })
          } else {
            addUser(saveForm).then(response => {
              this.$message.success('新增成功', 3)
              this.open = false
              this.$emit('ok')
            })
          }
        } else {
          return false
        }
      })
    },
    onLoadData(treeNode) {
      return new Promise(resolve => {
        if (treeNode.dataRef.children) {
          resolve()
          return
        }
        listDeptTree(treeNode.dataRef.id, 1).then(response => {
          treeNode.dataRef.children = response.data
          resolve()
        })
      })
    },
    checkUserNameUnique(role, value, callback) {
      const msg = '登陆名称已存在'
      if (value === '') {
        callback()
      } else {
        checkUserNameUnique(value).then(response => {
          if ((response.code = 200 && response.success == false)) {
            callback(new Error(msg))
          } else {
            callback()
          }
        })
      }
    },
    checkPhoneUnique(role, value, callback) {
      const msg = '手机号已存在'
      if (value === '') {
        callback()
      } else {
        checkPhoneUnique(value, this.form.userId !== undefined ? this.form.userId : '').then(response => {
          if ((response.code = 200 && response.success == false)) {
            callback(new Error(msg))
          } else {
            callback()
          }
        })
      }
    }
  }
}
