import request from '@/utils/request'

// 查询部门列表
export function listDept(searchInfo) {
  return request({
    url: '/sys/dept/tree',
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data: searchInfo,
  })
}

// 获取父节点下的部门
export function getDeptOfParent(parentId) {
  let data = 'parentId=' + parentId
  return request({
    url: '/sys/dept/children/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  })
}

// 查询部门树列表（新增/修改调用）
export function deptTree(deptId) {
  let data = deptId ? 'deptId=' + deptId : ''
  return request({
    url: '/sys/dept/parent/tree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  })
}

export function getDeptTree() {
  let data = 'deptId=0'
  return request({
    url: '/sys/dept/parent/tree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  })
}

// 查询部门详细
export function getDept(deptId) {
  let data = deptId ? 'deptId=' + deptId : ''
  return request({
    url: '/sys/dept/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  })
}

// 根据角色ID查询部门树结构
export function roleDeptTreeselect(roleId) {
  return request({
    url: '/system/dept/roleDeptTreeselect/' + roleId,
    method: 'get',
  })
}

// 新增部门
export function addDept(data) {
  return request({
    url: '/sys/dept/add',
    method: 'post',
    data: data,
  })
}

// 修改部门
export function updateDept(data) {
  return request({
    url: '/sys/dept/update',
    method: 'post',
    data: data,
  })
}

// 删除部门
export function delDept(params) {
  return request({
    url: '/sys/dept/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 部门最大排序
export function getNextSort(parentId) {
  let data = 'parentId=' + parentId
  return request({
    url: '/sys/dept/getNextSort/',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  })
}

// 校验部门名称是否存在
export function validateDeptNameUnique(deptName, parentId, id) {
  if (id === undefined) {
    id = ''
  }
  return request({
    url: '/system/dept/validateDeptNameUnique/' + deptName + '/' + parentId + '/' + id,
    method: 'get',
  })
}

// 按部门分组人员树
export function userSelectTree(params) {
  return request({
    url: '/sys/role/dept/user/tree',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 同步钉钉组织架构
export function syncDingDept() {
  return request({
    url: '/sys/dept/dingtalk/sync',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 部门树检索
export function searchDept(searchInfo) {
  return request({
    url: '/system/dept/search',
    method: 'get',
    params: searchInfo,
  })
}
// 部门树检索
export function searchDeptList(searchInfo) {
  return request({
    url: '/system/dept/searchDeptList',
    method: 'get',
    params: searchInfo,
  })
}

// 按部门树检索用户
export function searchDeptUserList(searchInfo) {
  return request({
    url: '/system/dept/searchDeptUserList',
    method: 'get',
    params: searchInfo,
  })
}

// 查询部门详细
export function getDeptInfoByIds(userIds) {
  return request({
    url: '/system/dept/getDeptInfoByIds',
    method: 'post',
    data: userIds,
  })
}
