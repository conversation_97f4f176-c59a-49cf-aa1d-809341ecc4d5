<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="菜单名称">
        <a-input v-model="queryParam.menuName" placeholder="请输入菜单名称" allow-clear />
      </a-form-item>

      <a-form-item label="菜单编码">
        <a-input v-model="queryParam.menuCode" placeholder="请输入菜单编码" allow-clear />
      </a-form-item>

      <a-form-item label="状态">
        <a-select placeholder="请选择" v-model="queryParam.isDisabled" style="width: 100%" allow-clear>
          <a-select-option v-for="(d, index) in statusOptions" :key="index" :value="d.dictKey">
            {{ d.dictValue }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="路由地址">
        <a-input v-model="queryParam.route" placeholder="请输入路由地址" allow-clear />
      </a-form-item>

      <a-form-item label="权限标识">
        <a-input v-model="queryParam.perms" placeholder="请输入权限标识" allow-clear />
      </a-form-item>

      <a-form-item label="组件地址">
        <a-input v-model="queryParam.component" placeholder="请输入组件地址" allow-clear />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="菜单管理"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          :tablePage="false"
          :stripe="false"
          @selectChange="selectChange"
          :tree-config="{
            rowField: 'id',
            parentField: 'parentId',
            indent: 16,
            reserve: true
          }"
          :row-config="{ isHover: true, keyField: 'id' }"
          :checkbox-config="{ highlight: true, showHeader: true }"
        >
          <div class="table-operations" slot="button">
            <a-space align="center">
              <a-button type="primary" @click="handleAdd()">
                <a-icon type="plus" />
                新增
              </a-button>
              <a-button type="primary" @click="importToRoot">
                <a-icon type="import" />
                导入
              </a-button>
              <a-button type="danger" v-if="isChecked" @click="handleDelete">
                <a-icon type="delete" />
                删除
              </a-button>
            </a-space>
          </div>
        </VxeTable>
      </template>
    </VxeTableForm>

    <menu-add-form
      v-if="showAddModal"
      ref="menuAddForm"
      :menuOptions="menuOptions"
      :menuTypeOptions="menuTypeOptions"
      :linkOptions="linkOptions"
      @select-tree="getTreeselect"
      @ok="getList"
      @close="showAddModal = false"
    />
    <menu-edit-form
      v-if="showEditModal"
      ref="menuEditForm"
      :menuOptions="menuOptions"
      :menuTypeOptions="menuTypeOptions"
      :linkOptions="linkOptions"
      @select-tree="getTreeselect"
      @ok="getList"
      @close="showEditModal = false"
    />

    <ImportExportJsonModal
      v-if="showImportExportJsonModal"
      ref="importExportJsonModalRef"
      @sendImportRequest="sendImportRequest"
      @afterImport="getList"
      @close="showImportExportJsonModal = false"
    />
  </div>
</template>
<script>
  import {
    listMenu,
    copyMenu,
    menuTree,
    getMenuOfParent,
    delMenu,
    searchMenuList,
    menuStatus,
    exportMenu,
    importMenu
  } from '@/api/system/menu'

  import MenuEditForm from './modules/MenuEditForm'
  import MenuAddForm from './modules/MenuAddForm'
  import ImportExportJsonModal from '@/views/util/ImportExportJsonModal'
  import Cookies from 'js-cookie'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import { MenusIcon } from 'hhzk-vue2-components'

  export default {
    name: 'Menu',
    components: {
      VxeTable,
      VxeTableForm,
      MenuEditForm,
      MenuAddForm,
      ImportExportJsonModal
    },
    data() {
      return {
        showImportExportJsonModal: false,
        showAddModal: false,
        showEditModal: false,
        iconVisible: false,
        list: [],
        isChecked: false,
        ids: [],
        names: [],
        menuOptions: [],
        nodeList: [],
        menuTypeOptions: [],
        loading: false,
        currentMenuId: null,
        // 状态数据字典
        statusOptions: [],
        linkOptions: [],
        applicationData: [], //默认应用数据
        parentNodeId: 0,
        isExpandId: null,
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        queryParam: {
          menuName: undefined,
          isDisabled: undefined,
          menuCode: undefined,
          component: undefined,
          perms: undefined,
          route: undefined
        },
        columns: [
          { type: 'checkbox', width: 30, align: 'center' },
          {
            title: '菜单名称',
            field: 'menuName',
            minWidth: 220,
            treeNode: true,
            slots: {
              default: ({ row, rowIndex }) => {
                const text = row.menuName
                return (
                  <span>
                    <MenusIcon
                      iconClass={row.icon || ''}
                      class='depIcon'
                      style={{ fontSize: '16px', marginTop: '0px' }}
                    />
                    {text.indexOf(this.queryParam.menuName) > -1 ? (
                      <span>
                        {text.substr(0, text.indexOf(this.queryParam.menuName))}
                        <span style='color: #f50'>{this.queryParam.menuName}</span>
                        {text.substr(text.indexOf(this.queryParam.menuName) + this.queryParam.menuName.length)}
                      </span>
                    ) : (
                      <span>{text}</span>
                    )}
                  </span>
                )
              }
            }
          },
          {
            title: '菜单编码',
            field: 'menuCode',
            minWidth: 180
          },

          {
            title: '菜单类型',
            field: 'type',
            width: 80,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return <a-tag color={this.menuTypeFilter(row.type)}>{this.menuTypeFormat(row)}</a-tag>
              }
            }
          },
          {
            title: '路由地址',
            field: 'route',
            showOverflow: 'tooltip',
            minWidth: 120,
            align: 'center'
          },
          {
            title: '组件路径',
            field: 'component',
            minWidth: 160,
            showOverflow: 'tooltip',
            align: 'center'
          },
          {
            title: '权限标识',
            field: 'perms',
            showOverflow: 'tooltip',
            minWidth: 80,
            align: 'center'
          },
          {
            title: '排序',
            field: 'sort',
            align: 'center',
            width: 60,
            slots: {
              default: ({ row, rowIndex }) => {
                return <a-tag style='width: 50px'>{row.sort}</a-tag>
              }
            }
          },
          {
            title: '状态',
            field: 'isDisabled',
            minWidth: 100,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <div class='is-disabled'>
                    <a-switch
                      slot='actions'
                      size='small'
                      style={{ 'font-size': '10px', 'background-color': row.isDisabled == 0 ? '#52c41a' : '#fc011a' }}
                      checked={row.isDisabled == 0}
                      onChange={$event => this.onChangeStatus($event, row)}
                    />
                    <span
                      style={{
                        'font-size': '12px',
                        color: row.isDisabled == 0 ? '#52c41a' : '#fc011a',
                        'margin-left': '5px'
                      }}
                    >
                      {row.isDisabled == '0' ? '正常' : '停用'}
                    </span>
                  </div>
                )
              }
            }
          },
          {
            title: '操作',
            field: 'operate',
            width: 300,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a-button type='link' onClick={() => this.handleUpdate(row)}>
                      修改
                    </a-button>
                    {row.isDisabled == '0' && row.type != '4' && <a-divider type='vertical' />}
                    {row.isDisabled == '0' && row.type != '4' && (
                      <a-button type='link' onClick={() => this.handleAdd(row)}>
                        添加子菜单
                      </a-button>
                    )}

                    {(row.isLeaf == 0 ||
                      (row.isLeaf == 1 && (row.type == 1 || row.type == 2 || row.type == 3 || row.type == 4))) && (
                      <a-divider type='vertical' />
                    )}
                    <a-button type='link' onClick={() => this.export(row)}>
                      导出
                    </a-button>
                    <a-divider type='vertical' />
                    <a-button type='link' onClick={() => this.import(row)}>
                      导入
                    </a-button>
                    <a-divider type='vertical' />
                    <a-button type='link' onClick={() => this.handleCopy(row)}>
                      复制
                    </a-button>
                    <a-divider type='vertical' />
                    <a-button type='link' onClick={() => this.handleDelete(row)}>
                      删除
                    </a-button>
                  </span>
                )
              }
            }
          }
        ]
      }
    },
    created() {
      this.getList()
      this.getTreeselect()
      this.getDicts('isLink').then(response => {
        this.linkOptions = response.data
      })
      this.getDicts('isDisabled').then(response => {
        this.statusOptions = response.data
      })
      this.menuTypeOptions = [
        { menuTypeValue: '1', menuTypeLabel: '应用', menuTypeLevel: 'Applications' },
        { menuTypeValue: '2', menuTypeLabel: '目录', menuTypeLevel: 'Catalogue' },
        { menuTypeValue: '3', menuTypeLabel: '菜单', menuTypeLevel: 'Menu' },
        { menuTypeValue: '4', menuTypeLabel: '按钮', menuTypeLevel: 'Button' }
      ]
    },
    computed: {},
    watch: {},
    methods: {
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.menuId)
        this.names = valObj.records.map(item => item.menuName)
        this.isChecked = !!valObj.records.length
      },

      /** 查询定时任务列表 */
      getList() {
        this.loading = true

        searchMenuList(this.queryParam).then(response => {
          if (response.data.length !== 0) {
            this.list = this.changeIconState(response.data)
            this.loading = false
          } else {
            this.list = []
          }
          this.loading = false
        })
      },
      changeIconState(data) {
        for (let i in data) {
          data[i].slots = {
            icon: data[i].type == 0 ? 'org' : data[i].type == 1 ? 'company' : data[i].type == 2 ? 'dept' : ' '
          }
          if (data[i].children) {
            this.changeIconState(data[i].children)
          }
        }
        return data
      },
      changeState(data) {
        for (let i in data) {
          data[i].children = data[i].isLeaf == 1 ? [] : []
          data[i].slots = {
            icon: data[i].type == 0 ? 'org' : data[i].type == 1 ? 'company' : data[i].type == 2 ? 'dept' : ' '
          }
        }
        return data
      },
      menuTypeFilter(type) {
        let value = '#108ee9'
        if (type == '2') {
          value = '#2db7f5'
        } else if (type == '3') {
          value = '#87d068'
        } else if (type == '4') {
          value = '#6a91ed'
        }
        return value
      },
      menuTypeFormat(row) {
        if (row.type == '1') {
          return '应用'
        } else if (row.type == '2') {
          return '目录'
        } else if (row.type == '3') {
          return '菜单'
        } else if (row.type == '4') {
          return '按钮'
        }
      },
      /** 搜索按钮操作 */
      handleQuery() {
        if (
          (this.queryParam.menuName === undefined &&
            this.queryParam.isDisabled === undefined &&
            this.queryParam.menuCode === undefined &&
            this.queryParam.component === undefined &&
            this.queryParam.perms === undefined &&
            this.queryParam.route === undefined) ||
          (this.queryParam.menuName === '' &&
            this.queryParam.isDisabled === '' &&
            this.queryParam.menuCode === '' &&
            this.queryParam.component === '' &&
            this.queryParam.perms === '' &&
            this.queryParam.route === '')
        ) {
          this.getList()
        } else {
          this.loading = true
          searchMenuList(this.queryParam).then(response => {
            if (response.data.length !== 0) {
              this.list = this.changeIconState(response.data)
            } else {
              this.list = []
            }
            this.loading = false
          })
        }
      },
      export(record) {
        this.showImportExportJsonModal = true
        let that = this
        this.$nextTick(() => {
          that.$refs.importExportJsonModalRef.showExport({
            action: 'export',
            title: `导出菜单【${record.menuName}】`,
            id: record.menuId
          })
          that.$refs.importExportJsonModalRef.setModalLoading(true)
          exportMenu(record.menuId)
            .then(res => {
              that.$refs.importExportJsonModalRef.setContent(res.data)
              that.$refs.importExportJsonModalRef.setModalLoading(false)
            })
            .catch(err => {
              that.$refs.importExportJsonModalRef.setModalLoading(false)
            })
        })
      },
      importToRoot() {
        this.import({
          menuId: 0,
          menuName: '根目录'
        })
      },
      import(record) {
        this.showImportExportJsonModal = true
        this.$nextTick(() => {
          this.$refs.importExportJsonModalRef.showImport({
            action: 'import',
            title: `导入到菜单【${record.menuName}】`,
            id: record.menuId,
            content: ''
          })
        })
      },
      sendImportRequest(param) {
        importMenu({ menuId: param.id, subMenus: param.content })
          .then(res => {
            this.$refs.importExportJsonModalRef.handleImportResult(res)
          })
          .catch(err => {
            this.$refs.importExportJsonModalRef.setLoading(false)
            if (err) {
              console.log(err)
            }
          })
      },
      handleCopy(record) {
        this.$confirm({
          title: '确认复制所选中数据?',
          content: '当前选中名称为"' + record.menuName + '"的数据',
          onOk: () => {
            return copyMenu({ menuId: record.menuId }).then(res => {
              this.getList()
              this.$message.success(`复制成功`, 3)
            })
          },
          onCancel() {}
        })
      },
      /** 新增 **/
      handleAdd(record) {
        this.showAddModal = true
        this.$nextTick(() => this.$refs.menuAddForm.handleAdd(record))
      },
      /** 修改 **/
      handleUpdate(record) {
        this.showEditModal = true
        this.currentMenuId = record.menuId
        this.$nextTick(() => this.$refs.menuEditForm.handleUpdate(record))
      },
      /** 更新菜单状态 (0-正常 1-停用)**/
      onChangeStatus(e, record) {
        var that = this
        const menuId = record.menuId
        let isMenuDisabled = 0
        let recordIsDisabled = !record.isDisabled
        isMenuDisabled = recordIsDisabled ? 1 : 0
        let isDisabledName = isMenuDisabled == 1 ? '停用' : '启用'
        this.$confirm({
          title: '是否"' + isDisabledName + '"所选中数据?',
          content: '当前选中的数据',
          onOk() {
            return menuStatus(isMenuDisabled, menuId).then(res => {
              that.getList()
            })
          },
          onCancel() {}
        })
      },
      getAllMenuNode(nodes) {
        if (!nodes || nodes.length === 0) {
          return []
        }
        nodes.forEach(node => {
          return this.getAllMenuNode(node.children)
        })
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          menuName: '',
          isDisabled: '',
          menuCode: '',
          component: '',
          perms: '',
          route: ''
        }
        Cookies.remove('isExpandId')
        this.handleQuery()
      },
      //更新值状态
      changeValueState(data) {
        for (let i in data) {
          data[i].isLeaf = data[i].isLeaf == 1 ? true : false
          data[i].slots = {
            icon: data[i].type == 0 ? 'org' : data[i].type == 1 ? 'company' : data[i].type == 2 ? 'dept' : ' '
          }
          if (data[i].children) {
            this.changeValueState(data[i].children)
          }
        }
        return data
      },
      /** 查询菜单下拉树结构 */
      getTreeselect() {
        menuTree(this.currentMenuId).then(response => {
          this.menuOptions = []
          let menuResponse = response.data
          this.menuOptions = this.changeValueState(menuResponse)
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const menuIds = row.menuId ? [row.menuId] : this.ids
        const names = row.menuName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return delMenu({ menuIds: menuIds?.join(',') }).then(res => {
              that.selectChange({ records: [] })
              that.getList()
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
            })
          },
          onCancel() {}
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .depIcon {
    color: #2f54eb;
    margin-right: 5px;
  }
  .is-disabled {
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
