import request from '@/utils/request'

const userApi = {
  Login: '/sys/casLogin',
  Logout: '/sys/logout',
  // get my info
  UserInfo: '/sys/user/get',
}

/**
 * casLogin func
 * @param parameter
 * @returns {*}
 */
export function ssoLogin(parameter) {
  return request({
    url: userApi.Login,
    method: 'post',
    data: parameter,
  })
}

/**
 * login func
 * @param parameter
 * @returns {*}
 */
export function loginBySms(parameter) {
  return request({
    url: '/system/sysSms/loginBySms',
    method: 'post',
    data: parameter,
  })
}
/**
 * getInfo
 * @param parameter
 * @returns {*}
 */

export function getInfo(parameter) {
  return request({
    url: userApi.UserInfo,
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: 'userId=' + parameter,
    //parameter
  })
}
/**
 * logout func
 * @param parameter token
 * @returns {*}
 */
export function logout() {
  return request({
    url: userApi.Logout,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
  })
}



// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false,
    },
    method: 'post',
    data: data,
  })
}
