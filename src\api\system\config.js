import request from '@/utils/request'

// 查询参数列表
export function listConfig(query) {
  return request({
    url: '/sys/config/page',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: query
  })
}

// 查询参数详细
export function getConfig(configId) {
  let data = configId ? 'configId=' + configId : ''
  return request({
    url: '/sys/config/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}

// 根据参数键名查询参数值
export function getConfigKey(configKey) {
  let data = configKey ? 'configKey=' + configKey : ''
  return request({
    url: '/sys/config/getValueByKey',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}

// 新增参数配置
export function saveConfig(data) {
  return request({
    url: '/sys/config/add',
    method: 'post',
    data: data
  })
}

// 新增参数配置
export function editConfig(data) {
  return request({
    url: '/sys/config/update',
    method: 'post',
    data: data
  })
}

// 删除参数配置
export function delConfig(configIds) {
  let data = configIds ? 'configIds=' + configIds : ''
  return request({
    url: '/sys/config/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: data
  })
}

//清除缓存
export function cleanCache() {
  return request({
    url: '/sys/config/cleanCache',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 导出
export function exportConfig(configIds) {
  return request({
    url: '/sys/config/export',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: 'configIds=' + configIds
  })
}

// 导入
export function importConfig(data) {
  return request({
    url: '/sys/config/import',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  })
}
