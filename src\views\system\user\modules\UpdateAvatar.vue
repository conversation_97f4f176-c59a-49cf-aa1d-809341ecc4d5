<template>
  <a-modal :title="title" width="200px" :visible="open" @ok="submitForm" @cancel="cancel">
    <a-form-model ref="form" :model="user">
      <!--  @click="$refs.modal.edit()" -->
      <div class="ant-upload-preview" @click="$refs.modal.edit()">
        <a-icon type="camera" class="upload-icon" />
        <div class="mask"></div>
        <img :src="user.avatar" v-if="user.avatar" style="border-radius: 0%" />
        <!--  -->
        <!-- <UploadFile
          :fileUrl.sync="user.avatar"
          :multiple="false"
          listType="picture-card"
          folderName="projectCover"
          accept=".png,.jpg,.jpeg"
        /> -->
        <!-- border-radius: 50%; -->
        <a v-if="!user.avatar">
          <a-avatar
            style="color: #fff; text-align: center; padding: 32% 0; background-color: #ff8801; border-radius: 5px"
          ></a-avatar>
        </a>

        <avatar-modal ref="modal" @ok="setavatar" />
      </div>
    </a-form-model>
  </a-modal>
</template>
<script>
  import { getUser, updateUser } from '@/api/system/user'

  import clickThrottle from '@/utils/clickThrottle'
  import md5 from 'md5'
  import { PasswordCheck } from '@/components'

  import AvatarModal from '@/views/account/settings/AvatarModal'
  import { mapGetters } from 'vuex'
  // import AvatarModal from './AvatarModal'
  import UploadFile from '@/components/UploadFile/index.vue'

  export default {
    name: 'ResetPassword',
    components: { PasswordCheck, UploadFile, AvatarModal },
    props: {},
    data() {
      const validateNewPass = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入新密码'))
        } else if (value.length <= 0) {
          callback(new Error('不能为空'))
        } else {
          if (this.form.confirmPassword !== '') {
            this.$refs.form.validateField('confirmPassword')
          }
          callback()
        }
      }
      const validateConfirmPass = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请再次输入新密码确认'))
        } else if (value !== this.form.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      }
      return {
        title: '修改头像',
        open: false,
        childrenDrawer: false,
        formLayout: 'horizontal',
        msgText: '',
        newpwd: '',
        // form: {
        //   username: undefined,
        //   newPassword: undefined,
        //   confirmPassword: undefined,
        // },
        // rules: {
        //   newPassword: [{ required: true, validator: validateNewPass, trigger: 'change' }],
        //   confirmPassword: [{ required: true, validator: validateConfirmPass, trigger: 'change' }],
        // },

        saveRegex: null,
        errorTips: null,

        user: {},
        preview: {},
        option: {
          img: this.avatar,
          info: true,
          size: 1,
          outputType: 'jpeg',
          canScale: false,
          autoCrop: true,
          // 只有自动截图开启 宽度高度才生效
          autoCropWidth: 180,
          autoCropHeight: 180,
          fixedBox: true,
          // 开启宽度和高度比例
          fixed: true,
          fixedNumber: [1, 1],
        },
      }
    },
    created() {
      // getConfigKey('sys.userPassword.regex').then(res => {
      //   this.saveRegex = new RegExp(res.data)
      // })
      // getConfigKey('sys.userPassword.errorTips').then(res => {
      //   this.errorTips = res.data
      // })
      // this.getUser()
    },

    computed: {
      ...mapGetters(['avatar']),
    },
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      handleUpdateAvatar(row) {
        // this.form = {
        //   userId: row.userId,
        //   username: row.username,
        // }

        getUser(row.userId).then(response => {
          const dataValue = response.data
          this.user = dataValue
          // this.user.avatar = 'http://**************:18000/snap/22_113/channel_113.jpg?t=1702349542640305500'

          // this.getRShowName(this.user.name)
        })

        this.open = true
      },

      setavatar(url) {
        // this.option.img = url
        this.user.avatar = url
      },
      /** 重置密码按钮操作 */
      submitForm: function () {
        if (!clickThrottle(5000)) return
        this.$refs.form.validate(valid => {
          if (valid) {
            // this.user.avatar = 'http://**************:18000/snap/22_113/channel_113.jpg?t=1702349542640305500' //this.option.img

            updateUser(this.user).then(response => {
              this.$message.success('修改成功', 3)
              this.open = false
              // this.$emit('ok')
              this.$emit('close')
            })

            // if (!this.saveRegex.test(this.form.newPassword)) {
            //   this.$message.warning(this.errorTips, 3)
            //   return
            // }
            // let md5Pwd = md5(this.form.newPassword)
            // resetUserPwd(this.form.userId, md5Pwd).then(response => {
            // this.$message.success('修改成功', 3)
            // this.open = false
            //   this.$emit('close')
            // })
          } else {
            return false
          }
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  .avatar-upload-wrapper {
    height: 200px;
    width: 100%;
  }

  .ant-upload-preview {
    position: relative;
    margin: 0 25px;
    width: 100px;
    height: 100px;
    border-radius: 5px;
    // border-radius: 50%;
    //box-shadow: 0 0 1px #ccc;

    .upload-icon {
      position: absolute;
      opacity: 0;
      // top: 23px;
      // left: 23px;
      font-size: 1rem;
      padding: 10px 8px;
      background: rgba(222, 221, 221, 0.65);
      border-radius: 5px;
      // border-radius: 50%;
    }

    &:hover .upload-icon {
      opacity: 1;
    }

    .mask {
      opacity: 0;
      position: absolute;
      background: rgba(0, 0, 0, 0.4);
      cursor: pointer;
      transition: opacity 0.4s;

      &:hover {
        opacity: 1;
      }

      i {
        font-size: 1rem;
        position: absolute;
        top: 50%;
        left: 50%;
        // margin-left: -1rem;
        // margin-top: -1rem;
        color: #d6d6d6;
      }
    }

    img,
    .mask {
      width: 100px;
      height: 100px;
      padding: 4px;
      border-radius: 5px;
      // border-radius: 50%;
      overflow: hidden;
    }
  }

  .ant-avatar {
    width: 100%;
    height: 100%;
    font-size: 24px;
    border-radius: 4px;
    vertical-align: middle;
    margin-right: 8px;
  }
</style>
