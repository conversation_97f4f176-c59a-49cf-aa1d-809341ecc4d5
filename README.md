<h1 align="center">用户中心</h1>


## 简介

**用户中心**

> 用户中心是基于Vue 的前后端分离权限管理系统。
>
> 拥有用户管理、部门管理、岗位管理、菜单管理、角色管理、字典管理、参数管理、通知公告、操作日志、登录日志、在线用户、定时任务、代码生成、系统接口、服务监控、在线构建器、连接池监视等功能。

### 技术选型

编号|技术|版本号|备注
--|:--:|--:|--:
1|Vue|V2.6.12|前端技术框架Vue.js  
2|Vuex|V3.1.1|Vue.js应用程序开发的状态管理  
3|Ant-Design-VUE|V1.7.2|前端UI库  
4|Axios|V0.19.0|基于promise的网络请求库
5|Less|V3.0.4|层叠样式表语言 
6|ECharts|V5.0.0|数据可视化图表库
7|Webpack|V1.3.12|代码编译工具




## 开始使用

1. 环境准备
   * 运行启动用户中心
   * 安装[node](http://nodejs.org/)和[git](https://git-scm.com/)

    ```
    node version V12+
    ```
2. 安装

   ```shell
   git clone ssh://****************:11922/WaterBaseline/fe-water-baseline-admin.git    
   ```

3. 本地开发

   配置文件-开发环境
   
   ```shell
   #.env.development   
   NODE_ENV=development
   VUE_APP_PREVIEW=true
   #VUE_APP_PORT 后端服务访问地址
   VUE_APP_BASE_API=/api
   VUE_APP_PORT = 'http://**************:8810'  
   ```
   配置文件-生产环境
   ```shell
   #.env.production   
   NODE_ENV=production
   VUE_APP_PREVIEW=true
   #VUE_APP_PORT 后端服务访问地址
   VUE_APP_BASE_API='http://**************:8810'
   VUE_APP_PORT = ''
   ```
   配置文件-测试环境
   ```shell
   #.env.test   
   NODE_ENV=testing
   VUE_APP_PREVIEW=true
   #VUE_APP_PORT 后端服务访问地址
   VUE_APP_BASE_API='http://**************:8810' 
   VUE_APP_PORT =''
   ```

   进入项目根目录

   ```shell
   npm install
   ```

   > 若耗时太长可使用`npm install --registry=https://registry.npm.taobao.org`

   ```shell
   npm run dev
   ```

   > 打开浏览器访问 [http://localhost:8000]

  
  
   项目打包
   ```shell
   npm run build
   ```

   测试环境项目打包
   ```shell
   npm run build:test
   ```

   开发环境项目打包
   ```shell
   npm run build:dev
   ```

   生产环境项目打包
   ```shell
   npm run build:prod
   ```




## 联系

如果您发现了什么bug，或者有什么界面建议或意见，请联系**************。








