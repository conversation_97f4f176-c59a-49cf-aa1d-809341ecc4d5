<template>
  <div>
    <dict-data-edit-form
      v-if="showEditSubModal"
      ref="dictDataEditForm"
      :title="title"
      :dictCode="dictCode"
      :dictId="dictId"
      :statusOptions="statusOptions"
      @ok="getList"
      @close="showEditSubModal = false"
    />
    <dict-data-add-form
      v-if="showAddSubModal"
      ref="dictDataAddForm"
      :dictCode="dictCode"
      :dictId="dictId"
      :statusOptions="statusOptions"
      :title="title"
      @ok="getList"
      @close="showAddSubModal = false"
    />
    <a-card :bordered="false">
      <div class="table-operations" style="padding: 10px">
        <div class="table_title">{{ title }}</div>
        <a-button type="primary" @click="handleSubAdd()">
          <a-icon type="plus" />
          新增
        </a-button>
        <a-button type="danger" v-if="!multiple" @click="handleSubDelete">
          <a-icon type="delete" />
          删除
        </a-button>
        <a-tooltip title="刷新">
          <a-icon @click="getList" class="action" :type="loading ? 'loading' : 'reload'" />
        </a-tooltip>
      </div>
      <a-table
        :loading="loading"
        rowKey="dictDataId"
        size="middle"
        :columns="columns"
        :data-source="list"
        @refresh="getList"
        :pagination="false"
        :row-selection="{
          selectedRowKeys: selectedRowKeys,
          onChange: onSelectChange,
        }"
      >
        <span slot="isDisabled" slot-scope="text, record">
          <a-switch
            slot="actions"
            size="small"
            :style="{
              'font-size': '10px',
              'background-color': record.isDisabled == 0 ? '#52c41a' : '#fc011a',
            }"
            :checked="record.isDisabled == 0"
            @change="onChangeDataStatus(record)"
          />
          <span
            :style="{
              'font-size': '12px',
              color: record.isDisabled == 0 ? '#52c41a' : '#fc011a',
            }"
          >
            {{ record.isDisabled == '0' ? '正常' : '停用' }}
          </span>
        </span>
        <span slot="createTime" slot-scope="text, record">
          {{ parseTime(record.createdTime) }}
        </span>
        <span slot="operation" slot-scope="text, record">
          <a @click="handleSubUpdate(record)">修改</a>
          <a-divider type="vertical" />
          <a @click="handleSubDelete(record)">删除</a>
        </span>
      </a-table>
    </a-card>
  </div>
</template>

<script>
  import { listData, delData, exportData, dictDataStatus } from '@/api/system/dict/data'
  import DictDataEditForm from './modules/DictDataEditForm'
  import DictDataAddForm from './modules/DictDataAddForm'
  export default {
    inject: ['reload'],
    name: 'DictData',
    props: {
      dictCode: {
        type: String,
        require: true,
      },
      title: {
        type: String,
        default: '子表',
      },
      refreshData: {
        type: Number,
        require: true,
      },
    },
    components: {
      DictDataEditForm,
      DictDataAddForm,
    },
    data() {
      return {
        showAddSubModal: false,
        showEditSubModal: false,
        list: [],
        selectedRowKeys: [],
        selectedRows: [],
        // 高级搜索 展开/关闭
        advanced: false,
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        ids: [],
        dictLabels: [],
        loading: false,
        total: 0,
        dictId: 0,
        // 状态数据字典
        statusOptions: [
          { dictLabel: '正常', dictValue: '0', isDisabled: '0' },
          { dictLabel: '停用', dictValue: '1', isDisabled: '1' },
        ],
        queryDictCode: undefined,
        columns: [
          {
            title: '字典键',
            dataIndex: 'dictKey',
            ellipsis: true,
            align: 'center',
          },
          {
            title: '字典值',
            dataIndex: 'dictValue',
            ellipsis: true,
            align: 'center',
          },
          {
            title: '扩展字段1',
            dataIndex: 'option1',
            ellipsis: true,
            align: 'center',
          },
          {
            title: '扩展字段2',
            dataIndex: 'option2',
            ellipsis: true,
            align: 'center',
          },
          {
            title: '扩展字段3',
            dataIndex: 'option3',
            ellipsis: true,
            align: 'center',
          },
          {
            title: '扩展字段4',
            dataIndex: 'option4',
            ellipsis: true,
            align: 'center',
          },
          {
            title: '扩展字段5',
            dataIndex: 'option5',
            ellipsis: true,
            align: 'center',
          },
          {
            title: '字典排序',
            dataIndex: 'sort',
            ellipsis: true,
            align: 'center',
          },
          {
            title: '备注',
            dataIndex: 'remark',
            ellipsis: true,
            align: 'center',
          },
          {
            title: '创建时间',
            dataIndex: 'createdTime',
            ellipsis: true,
            scopedSlots: { customRender: 'createdTime' },
            align: 'center',
          },
          {
            title: '状态',
            dataIndex: 'isDisabled',
            scopedSlots: { customRender: 'isDisabled' },
            align: 'center',
          },
          {
            title: '操作',
            dataIndex: 'operation',
            width: '15%',
            // fixed: 'right',
            scopedSlots: { customRender: 'operation' },
            align: 'center',
          },
        ],
      }
    },
    filters: {},
    created() {
      this.queryDictCode = this.dictCode
      this.getList()
      // this.getDicts("sys_normal_disable").then((response) => {
      //   this.statusOptions = response.data;
      // });
    },
    computed: {},
    watch: {},
    methods: {
      getList() {
        if (this.refreshData == 2 || this.refreshData == 3) {
          this.reload
          this.list = []
        }
        let tempDictCode = ''
        this.showAddSubModal = false
        this.showEditSubModal = false
        this.loading = true
        tempDictCode = this.queryDictCode
        listData(tempDictCode).then(response => {
          this.list = response.data
          this.loading = false
        })
      },
      // 状态字典翻译
      statusFormat(row) {
        return this.selectDictLabel(this.statusOptions, row.status)
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.getList()
      },
      handleSubAdd() {
        this.showAddSubModal = true
        this.$nextTick(() => this.$refs.dictDataAddForm.handleAdd())
      },
      handleSubUpdate(record) {
        this.showEditSubModal = true
        this.$nextTick(() => this.$refs.dictDataEditForm.handleUpdate(record))
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = []
        this.queryDictCode = undefined
        this.handleQuery()
      },
      cancel() {
        this.resetQuery()
      },
      /** 状态启用 **/
      onChangeDataStatus(record) {
        var that = this
        const dictDataId = record.dictDataId

        let recordIsDisabled = !record.isDisabled
        let isDisabled = recordIsDisabled ? 1 : 0
        let isDisabledName = isDisabled == 1 ? '停用' : '启用'
        this.$confirm({
          title: '是否"' + isDisabledName + '"所选中数据?',
          content: '当前选中的数据',
          onOk() {
            return dictDataStatus(isDisabled, dictDataId).then(res => {
              that.refreshData == 3
              that.handleQuery()
              that.reload
            })
          },
          onCancel() {},
        })
      },
      onSelectChange(selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
        this.ids = this.selectedRows.map(item => item.dictDataId)
        this.dictLabels = this.selectedRows.map(item => item.dictValue)
        this.single = selectedRowKeys.length !== 1
        this.multiple = !selectedRowKeys.length
      },
      /** 删除按钮操作 */
      handleSubDelete(row) {
        var that = this
        const dictDataId = row.dictDataId || this.ids
        const dictLabels = row.dictValue || this.dictLabels
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中字典值为"' + dictLabels + '"的数据',
          onOk() {
            return delData(dictDataId).then(() => {
              that.onSelectChange([], [])
              that.getList()
              that.$message.success('删除成功', 3)
            })
          },
          onCancel() {},
        })
      },
      /** 导出按钮操作 */
      handleExport() {
        var that = this
        this.$confirm({
          title: '是否确认导出?',
          content: '此操作将导出当前条件下所有数据而非选中数据',
          onOk() {
            return exportData(that.queryDictCode).then(response => {
              that.download(response.msg)
              that.$message.success('导出成功', 3)
            })
          },
          onCancel() {},
        })
      },
    },
  }
</script>
