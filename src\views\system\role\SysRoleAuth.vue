<template>
  <div class="tree-table-page">
    <div class="left-panel">
      <!-- :isShowTableHeader="false" -->
      <VxeTable
        :isShowRefresh="false"
        :isShowSize="false"
        :isShowColumns="false"
        :isShowFull="false"
        ref="vxeTableRef"
        tableTitle="角色管理"
        :columns="columns"
        :tableData="list"
        :loading="loading"
        :isAdaptPageSize="true"
        @cell-click="cellClickEvent"
        highlight-current-row
        :current-row-key="currentRowKey"
        @adaptPageSizeChange="adaptPageSizeChange"
        @refresh="getList"
        :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
        @selectChange="selectChange"
        @handlePageChange="handlePageChange"
      >
        <div class="table-operations" slot="button">
          <a-input-search
            placeholder="请输入编码/名称"
            v-model="queryParam.keywords"
            style="width: 150px"
            @search="handleQuery"
          />
        </div>

        <span slot="roleName" style="cursor: pointer" slot-scope="{ text, record }">
          {{ text }}
        </span>
        <span slot="roleCode" style="cursor: pointer" slot-scope="{ text, record }">
          {{ text }}
        </span>
      </VxeTable>
    </div>
    <div class="right-panel">
      <a-card :bordered="false" style="min-height: calc(100vh - 125px)">
        <div
          v-if="selectItem.roleId !== undefined && selectItem.roleCode !== 'superadmin'"
          style="position: absolute; float: right; margin-right: 20px; color: '#4351E8'; top: 10px; right: 20px"
        >
          角色：{{ roleName }}
        </div>
        <a-tabs
          default-active-key="1"
          @change="tabChange"
          v-if="selectItem.roleId !== undefined && selectItem.roleCode !== 'superadmin'"
        >
          <a-tab-pane key="1" tab="菜单权限" style="padding-left: 20px; padding-right: 20px">
            <a-spin :spinning="spinning" :delay="delayTime" tip="Loading...">
              <a-card>
                <a slot="title">
                  菜单权限：
                  <a-checkbox @change="handleCheckedTreeExpand($event)" :checked="menuExpand">展开/折叠</a-checkbox>
                  <a-checkbox @change="handleCheckedTreeNodeAll($event)" :checked="menuNodeAll">全选/全不选</a-checkbox>
                  <!-- <a-checkbox @change="handleCheckedTreeConnect($event)" :checked="menuCheckStrictly">
                      父子联动
                    </a-checkbox> -->
                </a>
                <a slot="extra">
                  <a-button type="primary" @click="saveRoleMenu">保存</a-button>
                </a>
                <a-form-model
                  ref="form"
                  :model="form"
                  style="height: calc(100vh - 265px); overflow-y: auto; overflow-x: hidden; padding-left: 20px"
                >
                  <a-form-model-item>
                    <!-- :checkStrictly="!menuCheckStrictly" :checkStrictly="true"-->
                    <a-tree
                      :loading="loading"
                      v-model="menuCheckedKeys"
                      :show-line="true"
                      checkable
                      :expanded-keys="menuExpandedKeys"
                      :auto-expand-parent="autoExpandParent"
                      :tree-data="menuOptions"
                      @check="onCheck"
                      @expand="onExpandMenu"
                      :replaceFields="defaultProps"
                    />
                  </a-form-model-item>
                </a-form-model>
              </a-card>
            </a-spin>
          </a-tab-pane>
        </a-tabs>

        <a-result
          style="padding: 50px"
          v-if="selectItem.roleId !== undefined && selectItem.roleCode == 'superadmin'"
          status="success"
          title="超级管理员权限"
          sub-title="超级管理员不受权限控制,其余角色根据需求设置菜单权限"
        ></a-result>
      </a-card>
    </div>
  </div>
</template>

<script>
  import { listRole, saveRoleMenu, queryRole, getNewRoleMenu } from '@/api/system/role'
  import { searchMenuList } from '@/api/system/menu'
  import AdvanceTable from '@/components/pt/table/AdvanceTable'
  import DataScope from './modules/DataScope'
  import PortletScope from './modules/PortletScope'
  import clickThrottle from '@/utils/clickThrottle'
  import VxeTable from '@/components/VxeTable/Auth'
  import VxeTableForm from '@/components/VxeTableForm'

  export default {
    name: 'Role',
    components: {
      AdvanceTable,
      VxeTable,
      VxeTableForm,
      DataScope,
      PortletScope,
    },
    data() {
      return {
        currentRowKey: '',
        tableHeight: 'calc(100vh-125)',
        isChecked: false,
        ids: [],
        names: [],
        roleName: '',
        initNum: 1,
        spinning: false,
        delayTime: 200,
        showDataScopeModal: false,
        showPortletModal: false,
        tab1Flag: '', // 选中行是否变化标志
        tab2Flag: '', // 选中行是否变化标志
        tab3Flag: '', // 选中行是否变化标志
        currentSelectTabKey: '1',
        routeSelectRoleId: '', // 从角色管理跳转到集中授权页面时传入的角色ID，用于定位选中行
        list: [],
        // 表格缓存的数据 - 用来点击取消时回显数据
        cacheData: [],
        deleteData: [], // 可编辑表格待删除数据，数据库已存在数据界面假删除，保存到该集合，最终请求数据库删除
        subList: [],
        selectedRowKeys: [],
        selectedRows: [],
        // 高级搜索 展开/关闭
        advanced: false,
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        currentSelectRoleId: '',
        selectItem: {},
        selectIndex: 0,
        loading: false,
        total: 0,
        // 权限区域
        menuExpandedKeys: [],
        checkedParentId: null,
        autoExpandParent: false,
        menuCheckedKeys: [],
        halfCheckedKeys: [],
        menuOptions: [],
        menuExpand: false,
        menuNodeAll: false,
        menuCheckStrictly: true,
        // form: {
        //   menuCheckStrictly:false
        // },
        selectRoleId: '',
        defaultProps: {
          children: 'children',
          title: 'menuName',
          key: 'menuId',
        },
        // 状态数据字典
        statusOptions: [],
        // 日期范围
        dateRange: [],
        labelCol: {
          span: 6,
        },
        wrapperCol: {
          span: 18,
        },
        queryParam: {
          pageNum: 1,
          isDisabled: null,
          keywords: '',
          pageSize: 15,
          sort: [],
        },
        columns: [
          {
            title: '角色名称',
            field: 'roleName',
            showOverflow: 'tooltip',
          },
          {
            title: '角色编码',
            field: 'roleCode',
            showOverflow: 'tooltip',
          },
        ],
        // columns: [
        //   {
        //     title: '角色名称',
        //     dataIndex: 'roleName',
        //     ellipsis: true,
        //     scopedSlots: {
        //       customRender: 'roleName',
        //     },
        //   },
        //   {
        //     title: '角色编码',
        //     dataIndex: 'roleCode',
        //     ellipsis: true,
        //     scopedSlots: {
        //       customRender: 'roleCode',
        //     },
        //   },
        // ],
      }
    },
    filters: {},
    created() {
      this.getList()
    },
    computed: {},
    watch: {
      selectItem(val) {
        this.tabChange(this.currentSelectTabKey)
      },
      $route() {},
    },
    methods: {
      handleRowClick(row, column, cell, event) {
        this.currentRowKey = row.id || row._index // 使用 id 或者 _index 作为 key
      },
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = !pageSize ? 9 : pageSize
        this.getList()
      },
      statusFormat(row) {
        return this.selectDictLabel(this.statusOptions, row.status)
      },
      //更新值状态
      changeValueState(data) {
        for (let i in data) {
          data[i].isLeaf = data[i].isLeaf == 1 ? true : false
          if (data[i].children) {
            this.changeValueState(data[i].children)
          }
        }
        return data
      },
      tabChange(key) {
        if (this.selectItem.roleCode != 'superadmin') {
          this.currentSelectTabKey = key
          this.getRoleMenuList(this.selectItem)
        }
      },
      /** 查询列表 */
      getList() {
        queryRole(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total

          if (this.list?.length > 0 && this.initNum == 1) {
            this.$nextTick(() => (this.selectItem = this.list[0]))
            this.$nextTick(() => (this.roleName = this.list[0].roleName))
          }
        })
      },
      resetQuery() {
        this.queryParam = {
          pageNum: 1,
          isDisabled: null,
          keywords: '',
          sort: [],
        }
        this.handleQuery()
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        console.log('***** 333333 valObj', valObj)
        this.ids = valObj.records.map(item => item.configId)
        this.names = valObj.records.map(item => item.configName)
        this.isChecked = !!valObj.records?.length
      },
      getRoleMenuList(row) {
        this.spinning = true
        this.menuExpand = false
        this.menuNodeAll = false
        this.loading = true
        getNewRoleMenu(row?.roleId).then(res => {
          this.loading = false
          this.menuCheckedKeys = res.data.fullCheckedMenuIds //选中项
          this.halfCheckedKeys = res.data.halfCheckedMenuIds //半选项
        })

        let roleMenu = this.getRoleMenuTreeselect()
        this.form = row
        roleMenu.then(res => {
          let menuResponse = res.data
          this.menuOptions = this.changeValueState(menuResponse)
          this.treeExpandWithLevel(this.menuOptions, 1)
          this.menuExpandedKeys.push(0)
          console.log(' this.menuNewCheckedKeys', this.menuNewCheckedKeys)

          this.spinning = false
        })
      },
      treeExpandWithLevel(treeNodeList, level) {
        level--
        if (level !== 0) {
          treeNodeList.forEach(node => {
            this.menuExpandedKeys.push(node.id)
            if (node.children) {
              this.treeExpandWithLevel(node.children, level)
            }
          })
        }
      },
      onExpandMenu(expandedKeys) {
        this.menuExpandedKeys = expandedKeys
        this.autoExpandParent = false
      },
      getParentIdById(treeNodeList, checkedList) {
        let appData = treeNodeList[0].children
        let spData = null
        for (let project of appData) {
          if (project.menuCode == 'support-platform') {
            spData = project.children
          }
        }
        checkedList.forEach(item => {
          spData.forEach(res => {
            if (res.menuId == item && res.type == 2) {
              this.checkedParentId = res.parentId
            }
          })
        })
        return this.checkedParentId
      },
      onCheck(checkedKeys, info) {
        let arr = []
        if (!this.menuCheckStrictly) {
          for (var i of this.menuCheckedKeys.checked) {
            arr.push(i)
          }
          //调用方法 根据id获取type为2的parentId；
          arr.push(this.getParentIdById(this.menuOptions, this.menuCheckedKeys.checked))
          this.menuCheckedKeys.checked = [...new Set(arr)]
        }

        // if (!this.menuCheckStrictly) {
        //   console.log(6666)
        //   // 禁用父子联动
        //   let currentCheckedKeys = []
        //   if (this.menuCheckedKeys.checked) {
        //     currentCheckedKeys = Array.from(new Set(currentCheckedKeys.concat(this.menuCheckedKeys.checked)))
        //   }
        //   if (this.menuCheckedKeys.halfChecked) {
        //     currentCheckedKeys = Array.from(new Set(currentCheckedKeys.concat(this.menuCheckedKeys.halfChecked)))
        //   }
        //   this.menuCheckedKeys = currentCheckedKeys
        // } else {
        // 半选节点
        this.halfCheckedKeys = info.halfCheckedKeys
        this.menuCheckedKeys = checkedKeys
        // }
      },
      /** 根据角色ID查询菜单树结构 */
      getRoleMenuTreeselect() {
        return searchMenuList({ isDisabled: 0 }).then(response => {
          return response
        })
      },
      cellClickEvent({ row, column }) {
        console.log('******* 666666 3333333', row, column)
        this.selectItem = row
        this.selectIndex = row.roleId
        this.roleName = row.roleName
      },
      handleRowClick(row, column, cell, event) {
        this.selectItem = row
        this.selectIndex = row.roleId
        this.roleName = row.roleName
        console.log('******* 666666', row, column, cell, event)
      },
      handleCheckedTreeExpand(value) {
        this.menuExpand = !this.menuExpand
        if (value.target.checked) {
          const treeList = this.menuOptions
          this.treeExpandWithLevel(treeList, -1)
        } else {
          this.menuExpandedKeys = []
          this.treeExpandWithLevel(this.menuOptions, 1)
        }
      },
      handleCheckedTreeNodeAll(value) {
        this.menuNodeAll = !this.menuNodeAll
        if (value.target.checked) {
          this.getAllMenuNode(this.menuOptions)
        } else {
          this.menuCheckedKeys = []
          this.halfCheckedKeys = []
        }
      },
      getAllMenuNode(nodes) {
        if (!nodes || nodes?.length === 0) {
          return []
        }
        nodes.forEach(node => {
          this.menuCheckedKeys.push(node.id)
          return this.getAllMenuNode(node.children)
        })
      },
      // 树权限（父子联动）
      handleCheckedTreeConnect(value) {
        this.menuCheckStrictly = !this.menuCheckStrictly
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      onShowSizeChange(pageNum, pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      onSizeChange(pageNum, size) {
        this.queryParam.pageNum = 1
        this.queryParam.pageSize = size
        this.getList()
      },
      changeSize(pageNum, pageSize) {
        this.initNum = 2
        this.queryParam.pageNum = pageNum
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 提交按钮 */
      saveRoleMenu: function () {
        if (!clickThrottle(5000)) return
        this.$refs.form.validate(valid => {
          this.form.menuIds = this.menuCheckedKeys
          let saveList = this.form.menuIds.concat(this.halfCheckedKeys)
          let menuList = this.trimSpace(saveList)
          saveRoleMenu(this.form.roleId, menuList).then(response => {
            this.$message.success('保存成功', 3)
            // this.menuCheckedKeys = [];
            // this.open = false;
            this.$emit('ok')
          })
        })
      },
      //自定义过滤函数
      trimSpace(array) {
        for (var i = 0; i < array?.length; i++) {
          //这里为过滤的值
          if (array[i] == ' ' || array[i] == null || typeof array[i] == 'undefined' || array[i] == '  ') {
            array.splice(i, 1)
            i = i - 1
          }
        }
        return array
      },
      // 所有菜单节点数据
      getMenuAllCheckedKeys() {
        // 全选与半选
        return Array.from(new Set(this.menuCheckedKeys.concat(this.halfCheckedKeys)))
      },
      // 选中行样式
      rowClassName(row) {
        let flag = false
        if (row.roleId == this.selectItem.roleId) {
          flag = true
        }
        return flag ? 'changeBgcolor' : ''
      },
    },
  }
</script>
<style lang="less" scoped>
  // /deep/.changeBgcolor {
  //   // background-color: #f0f2f5 !important;
  //   background-color: #f0f5ff !important;
  // }
  // /deep/ .ant-table-fixed-header .ant-table-scroll .ant-table-header {
  //   margin-bottom: -20px !important;
  //   padding-bottom: 20px !important;
  //   overflow: auto !important;
  // }
  // /deep/ .ant-table-fixed-header > .ant-table-content > .ant-table-scroll > .ant-table-body {
  //   overflow-y: auto !important;
  // }

  // .myTable {
  //   height: calc(100vh - 160px);
  // }
  /* 自定义选中行样式 */
  .vxe-table--highlight-row td {
    background-color: #b3daff !important; /* 淡蓝色背景 */
  }

  /* 如果你还需要自定义悬停行样式 */
  .vxe-table--highlight-hover-row:hover td {
    background-color: #e6f2ff !important; /* 更淡的蓝色背景 */
  }
</style>

<style lang="less" scoped src="./custom.less"></style>
