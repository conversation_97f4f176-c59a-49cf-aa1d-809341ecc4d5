<template>
  <div class="loginbox" v-if="ticketRemark != ''">
    <a-result status="500" :title="ticketName" :sub-title="ticketRemark">
      <template #extra>
        <a-button type="primary" @click="casLogin">返回登录页</a-button>
      </template>
    </a-result>
  </div>
</template>

<script>
  import { mapActions } from 'vuex'
  import { timeFix } from '@/utils/util'
  import { getInfo, ssoLogin } from '@/api/login'
  import store from '@/store'
  import Cookies from 'js-cookie'
  import { getUser } from '@/api/system/user'
  import storage from 'store'
  import FingerprintJS from '@fingerprintjs/fingerprintjs'
  import QueryString from 'query-string'

  export default {
    components: {},
    data() {
      return {
        form: {
          username: 'lykj',
          password: '123456',
          appId: this.GLOBAL.APPID,
        },
        ssoForm: {
          appId: this.GLOBAL.APPID,
          service: '',
          ticket: '',
          fingerprint: '',
        },
        data: [],
        ticket: '',
        ticketRemark: '',
        logining: false,
      }
    },
    mounted() {
      setTimeout(() => {
        this.createFingerprint()
      }, 500)
    },
    methods: {
      applyLicense() {
        window.open('/applyLicense', '_blank')
      },
      ...mapActions(['Login', '']),
      ssoLogin() {
        const ticket = sessionStorage.getItem('ticket')
        if (!ticket) {
          // 认证平台跳回，并在地址栏包含ticket,获取ticket
          window.location.href = `${process.env.VUE_APP_CAS_URL}/login?service=${this.getUrl()}`
        } else {
          this.ssoForm.service = this.getUrl()

          this.ssoForm.ticket = ticket
          this.Login({ ...this.ssoForm })
            .then(res => this.loginSuccess(res))
            .catch(err => this.requestFailed(err))
            .finally(() => {
              sessionStorage.removeItem('ticket')
            })
        }
      },
      casLogin() {
        window.location.href = `${process.env.VUE_APP_CAS_URL}/login?service=${this.getUrl()}`
      },
      //登录成功
      async loginSuccess(res) {
        let username = ''
        this.$router.push({ path: this.$route.query?.redirect || '/' })
        await getInfo(storage.get('userId')).then(res => {
          username = res.data.name
        })
        // 延迟 1 秒显示欢迎信息
        setTimeout(() => {
          this.$notification.success({
            message: username,
            description: `${timeFix()}，欢迎您回来！`,
            duration: 5,
          })
        }, 1000)
      },
      //登录失败
      requestFailed(err) {
        if (Cookies.get('serveErr')) {
          this.data = JSON.parse(Cookies.get('serveErr'))
        } else {
          this.data = { code: err.data.code, message: err.data.message }
        }
        this.ticketName = '单点登录失败'
        this.ticketRemark = this.data.code + ':' + this.data.message
      },
      getUrl() {
        let url = `${window.location.origin}${
          window.location.search ? QueryString.parse(window.location.search).redirect : ''
        }`
        return url.endsWith('/') ? url.substring(0, url.length - 1) : url
      },
      // 创建浏览器指纹
      async createFingerprint() {
        // 初始化FingerprintJS
        const fp = await FingerprintJS.load()
        // 获取访问者的指纹
        const result = await fp.get()

        this.ssoForm.fingerprint = result.visitorId

        this.$nextTick(() => {
          this.ssoLogin()
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  .loginbox {
    position: relative;
    width: 800px;
    height: 600px;
    margin: auto;
    background: #ffffff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
  }

  .ticket {
    position: absolute;
    margin-top: 30%;
    margin-left: 30%;
  }

  .user-layout-login label {
    font-size: 12px !important;
  }
</style>
