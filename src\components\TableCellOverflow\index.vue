<script>
  export default {
    name: 'TableCellOverflow',
    props: {
      rowNum: { type: Number, default: 2 },
      content: { type: String, default: '' }
    },
    components: {},
    data() {
      return {}
    },
    methods: {},
    render() {
      return (
        <a-tooltip title={this.content}>
          <div class='text-content' style={{ '-webkit-line-clamp': this.rowNum }}>
            {this.content}
          </div>
        </a-tooltip>
      )
    }
  }
</script>

<style lang="less" scoped>
  .text-content {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 显示的行数 */
    overflow: hidden;
  }
</style>
