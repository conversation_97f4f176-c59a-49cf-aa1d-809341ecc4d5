.tree-table-page {
  display: flex;
  .left-panel {
    // background: linear-gradient(180deg, rgba(55, 114, 255, 0.2) 1%, rgba(55, 114, 255, 0) 15%);
    border-right: 1px solid #f2f3f5;
    background-color: #fff;
    // height: calc(100vh - 120px);
    border-radius: 8px;
    padding: 16px 10px;
    // background: red;

    // flex: 1;
    width: 500px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-radius: 0 8px 8px 0;
  }

  .right-panel {
    flex: 1;
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-radius: 8px;
  }
}
