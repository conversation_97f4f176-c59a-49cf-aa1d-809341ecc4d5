<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="部门名称">
        <a-input
          v-model="queryParam.deptName"
          placeholder="请输入部门名称"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>

      <a-form-item label="部门类别">
        <a-select placeholder="请选择部门类别" v-model="queryParam.type" style="width: 100%" allow-clear>
          <a-select-option v-for="(d, index) in deptTypeOptions" :key="index" :value="d.dictKey">
            {{ d.dictValue }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="部门管理"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          :tablePage="false"
          :stripe="false"
          @selectChange="selectChange"
          :tree-config="{
            rowField: 'id',
            parentField: 'parentId',
            indent: 20,
            reserve: true
          }"
          :row-config="{ isHover: true, keyField: 'id' }"
          :checkbox-config="{ highlight: true, showHeader: true }"
        >
          <div class="table-operations" slot="button">
            <a-space align="center">
              <!-- <a-button type="primary" :loading="dingLoading" @click="syncDingDept()">
                <a-icon type="plus" />
                同步钉钉组织机构
              </a-button> -->
              <a-button type="primary" @click="handleAdd()">
                <a-icon type="plus" />
                新增
              </a-button>
              <a-button type="danger" v-if="isChecked" @click="handleDelete">
                <a-icon type="delete" />
                删除
              </a-button>
            </a-space>
          </div>
        </VxeTable>
      </template>
    </VxeTableForm>

    <sys-dept-add-form
      v-if="showAddModal"
      ref="sysDeptAddForm"
      :deptTypeOptions="deptTypeOptions"
      :deptTypeEditOptions="deptTypeEditOptions"
      :deptOptions="deptOptions"
      @select-tree="getTreeselect"
      @ok="handleQuery"
      @close="showAddModal = false"
    />

    <sys-dept-edit-form
      v-if="showEditModal"
      ref="sysDeptEditForm"
      :deptTypeOptions="deptTypeOptions"
      :deptTypeEditOptions="deptTypeEditOptions"
      :deptOptions="deptOptions"
      @select-tree="getTreeselect"
      @ok="handleQuery"
      @close="showEditModal = false"
    />
  </div>
</template>

<script>
  import { listDept, delDept, getDeptOfParent, deptTree, listDeptExcludeChild, syncDingDept } from '@/api/system/dept'
  import SysDeptEditForm from './modules/SysDeptEditForm'
  import SysDeptAddForm from './modules/SysDeptAddForm'
  import { exportConfig } from '@/api/system/config'
  import ImportExcel from '@/components/pt/import/ImportExcel'
  import { importTemplate } from '@/api/system/user'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'

  export default {
    name: 'Dept',
    components: {
      SysDeptAddForm,
      SysDeptEditForm,
      ImportExcel,
      VxeTable,
      VxeTableForm
    },
    data() {
      return {
        isdefaultExpandAllRows: false,
        showAddModal: false,
        showEditModal: false,
        list: [],
        // 部门树选项
        deptOptions: [],
        parentDeptId: 0, //默认父节点

        ids: [],
        names: [],
        isChecked: false,
        loading: false,
        dingLoading: false,
        currentDeptId: null,
        // 状态数据字典
        deptTypeOptions: [],
        deptTypeEditOptions: [],
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        queryParam: {
          deptName: '',
          parentId: 0,
          remark: '',
          sort: 0,
          type: null
        },
        columns: [
          { type: 'checkbox', width: 30, align: 'center' },
          {
            title: '部门名称',
            field: 'deptName',
            minWidth: 250,
            treeNode: true,
            slots: {
              default: ({ row, rowIndex }) => {
                const text = row.deptName
                return (
                  <span>
                    {row.type == 1 && <SvgIcon iconClass='companyFill' class='depIcon' />}
                    {row.type == 2 && <SvgIcon iconClass='company' class='depIcon' />}
                    {row.type == 3 && <SvgIcon iconClass='connections' class='depIcon' />}

                    {text.indexOf(this.queryParam.deptName) > -1 ? (
                      <span>
                        {text.substr(0, text.indexOf(this.queryParam.deptName))}
                        <span style='color: #f50'>{this.queryParam.deptName}</span>
                        {text.substr(text.indexOf(this.queryParam.deptName) + this.queryParam.deptName.length)}
                      </span>
                    ) : (
                      <span v-else>{text}</span>
                    )}
                  </span>
                )
              }
            }
          },
          {
            title: '部门类别',
            field: 'type',
            minWidth: 120,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return <span>{row.type == '1' ? '机构' : row.type == '2' ? '部门' : ''}</span>
              }
            }
          },
          {
            title: '排序',
            field: 'sort',
            align: 'center'
          },
          {
            title: '备注',
            field: 'remark'
          },
          {
            title: '操作',
            field: 'operate',
            width: 200,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a-button type='link' onClick={() => this.handleUpdate(row)}>
                      修改
                    </a-button>
                    <a-divider type='vertical' />
                    <a-button type='link' onClick={() => this.handleAdd(row)}>
                      添加子部门
                    </a-button>
                    <a-divider type='vertical' />
                    <a-button type='link' onClick={() => this.handleDelete(row)}>
                      删除
                    </a-button>
                  </span>
                )
              }
            }
          }
        ]
      }
    },
    filters: {},
    created() {
      // this.getList();
      this.handleQuery()
      this.getTreeselect()
      this.getDicts('deptType').then(response => {
        this.deptTypeOptions = response.data
        this.deptTypeEditOptions = this.deptTypeOptions.filter(function (item) {
          return item.dictKey != 1
        })
      })
    },
    computed: {},
    watch: {},
    methods: {
      importTemplate(expanded, record) {
        importTemplate().then(response => {
          this.download(response.msg)
        })
      },

      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.deptId)
        this.names = valObj.records.map(item => item.deptName)
        this.isChecked = !!valObj.records.length
      },

      /** 查询菜单下拉树结构 */
      getTreeselect() {
        deptTree(this.currentDeptId).then(response => {
          if (response.code == 200 && response.success == true) {
            this.deptOptions = this.changeIconState(response.data)
          }
        })
      },
      changeIconState(data) {
        for (let i in data) {
          data[i].slots = {
            icon: data[i].type == 0 ? 'org' : data[i].type == 1 ? 'company' : data[i].type == 2 ? 'dept' : ' '
          }
          if (data[i].children) {
            this.changeIconState(data[i].children)
          }
        }
        return data
      },
      /** 查询定时任务列表 */
      getList() {
        this.loading = true
        getDeptOfParent(this.parentDeptId).then(response => {
          // 加载完数据后需要迭代计算需要展开的行数据
          this.list = response.data
          this.loading = false
        })
      },

      /** 搜索按钮操作 */
      handleQuery() {
        if (
          (this.queryParam.deptName === undefined && this.queryParam.type === undefined) ||
          (this.queryParam.deptName === '' && this.queryParam.type === '')
        ) {
          // this.expandedRowKeys = [];
          this.getList()
        } else {
          listDept(this.queryParam)
            .then(response => {
              if (response.data.length !== 0) {
                this.list = response.data

                this.selectChange({ records: [] })

                this.$nextTick(() => {
                  this.$refs.vxeTableRef.setTreeExpand(
                    this.list.map(el => el.id),
                    'id'
                  )
                })
              } else {
                this.list = []
              }
              this.loading = false
            })
            .catch(res => {
              console.log('listDept err', res)
            })
        }
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          deptName: '',
          parentId: 0,
          remark: '',
          sort: 0,
          type: ''
        }
        this.handleQuery()
      },
      handleAdd(record) {
        this.showAddModal = true
        this.$nextTick(() => this.$refs.sysDeptAddForm.handleAdd(record))
      },
      handleUpdate(record) {
        this.showEditModal = true
        this.currentDeptId = record.deptId
        this.$nextTick(() => this.$refs.sysDeptEditForm.handleUpdate(record))
      },
      /* 同步钉钉组织机构 */
      syncDingDept() {
        this.dingLoading = true
        syncDingDept().then(res => {
          this.$message.success('钉钉组织机构同步成功！', 2)
          this.dingLoading = false
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const deptIds = row.deptId ? [row.deptId] : this.ids
        const deptNames = row.deptName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + deptNames + '"的数据',
          onOk() {
            return delDept({ deptIds: deptIds.join(',') }).then(res => {
              that.selectChange({ records: [] })
              that.handleQuery()
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
            })
          },
          onCancel() {}
        })
      },
      /** 导入按钮操作 */
      handleImport() {
        alert('暂不支持')
      }
    }
  }
</script>

<style lang="less" scoped>
  .depIcon {
    color: #2f54eb;
    font-size: 20px;
    margin-right: 5px;
  }
</style>
