<template>
  <a-drawer
    width="35%"
    :label-col="4"
    :title="formTitle"
    :wrapper-col="14"
    :visible="open"
    :body-style="{ height: 'calc(100vh - 100px)', overflow: 'auto' }"
    @close="cancel"
  >
    <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
      <a-row class="form-row" :gutter="32">
        <!-- <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="字典编码" prop="dictCode">
            <a-input v-model="form.dictCode" :disabled="true" />
          </a-form-model-item>
        </a-col> -->

        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="字典键" prop="dictKey">
            <a-input v-model="form.dictKey" placeholder="请输入字典键" />
          </a-form-model-item>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="字典值" prop="dictValue">
            <a-input v-model="form.dictValue" placeholder="请输入字典值" />
          </a-form-model-item>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="扩展字段1" prop="option1">
            <a-input v-model="form.option1" placeholder="请输入扩展字段1" />
          </a-form-model-item>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="扩展字段2" prop="option2">
            <a-input v-model="form.option2" placeholder="请输入扩展字段2" />
          </a-form-model-item>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="扩展字段3" prop="option3">
            <a-input v-model="form.option3" placeholder="请输入扩展字段3" />
          </a-form-model-item>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="扩展字段4" prop="option4">
            <a-input v-model="form.option4" placeholder="请输入扩展字段4" />
          </a-form-model-item>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="扩展字段5" prop="option5">
            <a-input v-model="form.option5" placeholder="请输入扩展字段5" />
          </a-form-model-item>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="显示排序" prop="sort">
            <a-input-number v-model="form.sort" :min="0" style="width: 100%" />
          </a-form-model-item>
        </a-col>
      </a-row>

      <!-- <a-form-model-item label="状态" prop="isDisabled">
        <a-select placeholder="请选择" v-model="form.isDisabled">
          <a-select-option
            v-for="(d, index) in statusOptions"
            :key="index"
            :value="d.dictValue"
            >{{ d.dictLabel }}</a-select-option
          >
        </a-select>
      </a-form-model-item> -->
      <a-form-model-item label="备注" prop="remark">
        <a-input v-model="form.remark" placeholder="请输入内容" type="textarea" allow-clear />
      </a-form-model-item>
      <div class="bottom-control">
        <a-space>
          <a-button type="primary" @click="submitForm">保存</a-button>
          <a-button @click="cancel">取消</a-button>
        </a-space>
      </div>
    </a-form-model>
  </a-drawer>
</template>
<script>
  import DictDataForm from './DictDataForm'
  export default {
    ...DictDataForm
  }
</script>
