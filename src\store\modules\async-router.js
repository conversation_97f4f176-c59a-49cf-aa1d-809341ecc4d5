/**
 * 向后端请求用户的菜单，动态生成路由
 */
import { constantRouterMap } from '@/config/router.config'
import { generatorDynamicRouter } from '@/router/generator-routers'

const permission = {
  state: {
    routers: constantRouterMap,
    addRouters: []
  },
  mutations: {
    SET_ROUTERS: (state, routers) => {
      state.addRouters = constantRouterMap.concat(routers)
      state.routers = constantRouterMap.concat(routers)
      // state.routers = constantRouterMap
    }
  },
  actions: {
    GenerateRoutes({ commit }, data) {
      return new Promise(resolve => {
        // 先加载icon，icon加载promise较慢
        setTimeout(() => {
          generatorDynamicRouter(data).then(routers => {
            commit('SET_ROUTERS', routers)
            resolve()
          })
        }, 20)
      })
    }
  }
}

export default permission
