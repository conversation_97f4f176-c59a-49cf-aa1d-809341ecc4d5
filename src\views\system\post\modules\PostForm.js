import {
  getPost,
  savePost,
  checkPostCodeUnique,
  findMaxSort,
  checkPostNameUnique,
  upDatePost
} from '@/api/system/post'
import AntModal from '@/components/pt/dialog/AntModal'
import clickThrottle from '@/utils/clickThrottle'
export default {
  name: 'CreateForm',
  props: {
    statusOptions: {
      type: Array,
      required: true
    }
  },
  components: {
    AntModal
  },
  data() {
    return {
      loading: false,
      formTitle: '',
      // 表单参数
      form: {
        /* id: undefined,
        postCode: undefined,
        postName: undefined,
        sort: 0,
        status: '0',
        remark: undefined */
        postCode: '',
        postName: '',
        remark: '',
        sort: 0
      },
      open: false,
      rules: {
        postName: [{
          required: true,
          message: '岗位名称不能为空',
          trigger: 'blur'
        }, ],
        postCode: [{
          required: true,
          message: '岗位编码不能为空',
          trigger: 'blur'
        }, ],
        sort: [{
          required: true,
          message: '显示顺序不能为空',
          trigger: 'blur'
        }]
      }
    }
  },
  filters: {},
  created() {},
  computed: {},
  watch: {},
  methods: {
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
      this.$emit('close')
    },
    // 表单重置
    reset() {},
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      /** 获取最大编号 */
      findMaxSort().then(response => {
        this.form.sort = response.data
        this.open = true
        this.formTitle = '添加岗位'
      })
    },
    /** 修改按钮操作 */
    async handleUpdate(row, ids) {
      this.reset()
      const {
        data: res
      } = await getPost(row.postId)
      if (res.isDisabled == 0) {
        res.isDisabled = '0'
      } else if (res.isDisabled == 1) {
        res.isDisabled = '1'
      }
      this.form = res
      this.open = true
      this.formTitle = '修改岗位'
    },
    /** 提交按钮 */
    submitForm: function () {
      if (!clickThrottle(5000)) return;
      this.$refs.form.validate(valid => {
        if (valid) {
          savePost(this.form).then(response => {
            this.$message.success(
              '修改成功',
              3
            )
            this.open = false
            this.$emit('ok')
          })
        } else {
          return false
        }
      })
    },
    submitEditForm() {
      if (this.form.isDisabled == '0') {
        this.form.isDisabled = 0
      } else if (this.form.isDisabled == '1') {
        this.form.isDisabled = 1
      }
      this.$refs.form.validate(valid => {
        if (valid) {
          upDatePost(this.form).then(response => {
            this.$message.success(
              '修改成功',
              3
            )
            this.open = false
            this.$emit('ok')
          })
        } else {
          return false
        }
      })
    }
  }
}