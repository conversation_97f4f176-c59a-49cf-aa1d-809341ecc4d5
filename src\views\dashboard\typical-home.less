.typical-home {
  overflow: hidden;
  color: #666 !important;
  background-color: #f0f2f5;
  // 顶部列表
  .top-list {
    .ant-card-body {
      height: 66px;
      padding: 13px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      border-radius: 4px;
      span {
        color: #333;
      }
      > .anticon {
        margin-right: 16px;
        font-size: 24px;
        width: 40px;
        padding-top: 8px;
        height: 40px;
        vertical-align: middle;
        text-align: center;
        border-radius: 16px;
        background: #0081cc;
        color: #fff;
      }
    }
    .ant-col:last-child {
      cursor: pointer;
      .add-plus {
        color: #abafb5;
        border: 1px dashed #dadada;
        background: #f3f3f3;
      }
    }
  }
}
