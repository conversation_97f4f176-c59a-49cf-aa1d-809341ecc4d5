import Vue from 'vue'
/**
 * @description 超级用户常量
 */
const SUPERUSER = 'superadmin'
/**
 * @description 默认应用类型
 */
const APPID = 'admin'
/**
 * @description 默认密码
 */
const INITPWD = '123456'

/**
 * @description 性别
 */
const SEX = [
  { dictKey: 0, dictValue: '男' },
  { dictKey: 1, dictValue: '女' },
  { dictKey: 2, dictValue: '未知' }
]
//保存全局状态
let State = new Vue({
  data() {
    return {
      is_phone: false
    }
  }
})
export default {
  SUPERUSER,
  APPID,
  INITPWD,
  SEX,
  State
}
